// Generated by view binder compiler. Do not edit!
package com.bm.atool.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bm.atool.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDebugBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final Button btnStopSocket;

  @NonNull
  public final Button btnTestScript;

  @NonNull
  public final Button btnToutiaoScript;

  @NonNull
  public final TextView debugTitle;

  private FragmentDebugBinding(@NonNull RelativeLayout rootView, @NonNull Button btnStopSocket,
      @NonNull Button btnTestScript, @NonNull Button btnToutiaoScript,
      @NonNull TextView debugTitle) {
    this.rootView = rootView;
    this.btnStopSocket = btnStopSocket;
    this.btnTestScript = btnTestScript;
    this.btnToutiaoScript = btnToutiaoScript;
    this.debugTitle = debugTitle;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDebugBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDebugBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_debug, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDebugBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnStopSocket;
      Button btnStopSocket = ViewBindings.findChildViewById(rootView, id);
      if (btnStopSocket == null) {
        break missingId;
      }

      id = R.id.btnTestScript;
      Button btnTestScript = ViewBindings.findChildViewById(rootView, id);
      if (btnTestScript == null) {
        break missingId;
      }

      id = R.id.btnToutiaoScript;
      Button btnToutiaoScript = ViewBindings.findChildViewById(rootView, id);
      if (btnToutiaoScript == null) {
        break missingId;
      }

      id = R.id.debugTitle;
      TextView debugTitle = ViewBindings.findChildViewById(rootView, id);
      if (debugTitle == null) {
        break missingId;
      }

      return new FragmentDebugBinding((RelativeLayout) rootView, btnStopSocket, btnTestScript,
          btnToutiaoScript, debugTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

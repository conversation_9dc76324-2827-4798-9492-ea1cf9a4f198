{"permissions": {"allow": ["Bash(ls:*)", "Bash(./gradlew assembleDebug)", "Bash(git add:*)", "Bash(git commit -m \"修复WebView错误处理方法的编译错误，添加缺失的WebResourceError和WebResourceRequest导入\")", "Bash(adb install:*)", "<PERSON><PERSON>(./gradlew:*)", "WebSearch", "Bash(adb shell am start:*)", "Bash(find:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs"], "deny": [], "defaultMode": "acceptEdits"}}
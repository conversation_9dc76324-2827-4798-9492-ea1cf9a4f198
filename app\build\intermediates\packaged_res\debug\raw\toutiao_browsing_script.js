// 今日头条浏览脚本
console.log('=== 今日头条浏览脚本开始执行 ===');

// 全局配置
var CONFIG = {
    APP_PACKAGE: 'com.ss.android.article.news',
    WAIT_SHORT: 1000,
    WAIT_MEDIUM: 2000,
    WAIT_LONG: 3000,
    SWIPE_DURATION: 800,
    MAX_RETRY: 3,
    BROWSE_COUNT: 5
};

// 等待函数
function waitFor(ms) {
    sleep(ms);
    console.log('⏱️ 等待 ' + ms + 'ms');
}

// 随机等待函数
function randomWait(min, max) {
    var waitTime = Math.floor(Math.random() * (max - min + 1)) + min;
    waitFor(waitTime);
}

// 安全点击函数
function safeClick(selector, description) {
    try {
        var element = selector();
        if (element && element.exists()) {
            element.click();
            console.log('✓ 成功点击: ' + description);
            return true;
        } else {
            console.log('✗ 未找到元素: ' + description);
            return false;
        }
    } catch (e) {
        console.log('✗ 点击失败: ' + description + ', 错误: ' + e.message);
        return false;
    }
}

// 安全滑动函数
function safeSwipe(x1, y1, x2, y2, duration, description) {
    try {
        swipe(x1, y1, x2, y2, duration);
        console.log('✓ 成功滑动: ' + description);
        return true;
    } catch (e) {
        console.log('✗ 滑动失败: ' + description + ', 错误: ' + e.message);
        return false;
    }
}

// 检查应用是否已安装
function isAppInstalled(packageName) {
    try {
        var packages = context.getPackageManager().getInstalledPackages(0);
        for (var i = 0; i < packages.size(); i++) {
            if (packages.get(i).packageName.equals(packageName)) {
                return true;
            }
        }
        return false;
    } catch (e) {
        console.log('⚠️ 检查应用安装状态失败: ' + e.message);
        return false;
    }
}

// 启动今日头条
function launchToutiao() {
    console.log('🚀 正在启动今日头条...');

    // 检查应用是否已安装
    if (!isAppInstalled(CONFIG.APP_PACKAGE)) {
        console.log('❌ 今日头条应用未安装');
        return false;
    }

    try {
        // 尝试启动应用
        app.launch(CONFIG.APP_PACKAGE);
        waitFor(CONFIG.WAIT_LONG);

        // 检查是否成功启动
        if (currentPackage() === CONFIG.APP_PACKAGE) {
            console.log('✅ 今日头条启动成功');
            return true;
        } else {
            console.log('❌ 今日头条启动失败 - 当前包名: ' + currentPackage());
            return false;
        }
    } catch (e) {
        console.log('❌ 今日头条启动异常: ' + e.message);
        return false;
    }
}

// 处理可能的弹窗
function handlePopups() {
    console.log('🔍 检查并处理弹窗...');

    var popupSelectors = [
        function() { return text('跳过').findOne(1000); },
        function() { return text('取消').findOne(1000); },
        function() { return text('不感兴趣').findOne(1000); },
        function() { return text('关闭').findOne(1000); },
        function() { return desc('关闭').findOne(1000); },
        function() { return id('close').findOne(1000); }
    ];

    for (var i = 0; i < popupSelectors.length; i++) {
        if (safeClick(popupSelectors[i], '关闭弹窗 (方式' + (i + 1) + ')')) {
            waitFor(CONFIG.WAIT_SHORT);
            return true;
        }
    }

    console.log('ℹ️ 未发现弹窗');
    return false;
}

// 浏览新闻列表
function browseNewsList() {
    console.log('📰 开始浏览新闻列表...');

    for (var i = 0; i < CONFIG.BROWSE_COUNT; i++) {
        console.log('📖 第 ' + (i + 1) + ' 次浏览');

        // 处理可能的弹窗
        handlePopups();

        // 向上滑动浏览新闻
        var screenHeight = device.height;
        var screenWidth = device.width;

        safeSwipe(
            screenWidth / 2,
            screenHeight * 0.8,
            screenWidth / 2,
            screenHeight * 0.3,
            CONFIG.SWIPE_DURATION,
            '向上滑动浏览新闻'
        );

        randomWait(CONFIG.WAIT_MEDIUM, CONFIG.WAIT_LONG);

        // 尝试点击第一条新闻
        if (i === 2) {
            clickFirstNews();
        }
    }

    console.log('✅ 新闻列表浏览完成');
}

// 点击第一条新闻
function clickFirstNews() {
    console.log('🎯 尝试点击第一条新闻...');

    // 尝试多种可能的新闻元素选择器
    var selectors = [
        function() { return className('android.widget.TextView').clickable(true).findOne(2000); },
        function() { return className('android.view.ViewGroup').clickable(true).findOne(2000); },
        function() { return className('android.widget.LinearLayout').clickable(true).findOne(2000); }
    ];

    for (var i = 0; i < selectors.length; i++) {
        try {
            var elements = selectors[i]();
            if (elements && elements.exists()) {
                elements.click();
                console.log('✅ 成功点击新闻 (方式' + (i + 1) + ')');
                waitFor(CONFIG.WAIT_LONG);

                // 检查是否进入了新闻详情页
                if (isInNewsDetail()) {
                    readNewsContent();
                    goBack();
                    return true;
                }
            }
        } catch (e) {
            console.log('⚠️ 点击方式' + (i + 1) + '失败: ' + e.message);
        }
    }

    // 如果找不到具体元素，尝试点击屏幕中央偏上的位置
    try {
        var x = device.width / 2;
        var y = device.height * 0.35;
        click(x, y);
        console.log('🎯 点击屏幕位置: (' + x + ', ' + y + ')');
        waitFor(CONFIG.WAIT_LONG);

        if (isInNewsDetail()) {
            readNewsContent();
            goBack();
            return true;
        }
    } catch (e) {
        console.log('❌ 点击屏幕位置失败: ' + e.message);
    }

    console.log('❌ 无法点击新闻');
    return false;
}

// 检查是否在新闻详情页
function isInNewsDetail() {
    try {
        // 检查是否有返回按钮或者新闻详情页的特征元素
        return text('返回').exists() ||
               desc('返回').exists() ||
               className('android.webkit.WebView').exists() ||
               text('评论').exists();
    } catch (e) {
        return false;
    }
}

// 阅读新闻内容
function readNewsContent() {
    console.log('📖 正在阅读新闻内容...');

    // 模拟阅读，向下滑动几次
    for (var i = 0; i < 3; i++) {
        safeSwipe(
            device.width / 2,
            device.height * 0.7,
            device.width / 2,
            device.height * 0.3,
            600,
            '阅读新闻内容 - 向下滑动 ' + (i + 1)
        );
        randomWait(1000, 2000);

        // 随机停留，模拟真实阅读
        if (Math.random() > 0.5) {
            waitFor(CONFIG.WAIT_MEDIUM);
        }
    }

    console.log('✅ 新闻内容阅读完成');
}

// 返回上一页
function goBack() {
    console.log('🔙 返回上一页...');

    var retryCount = 0;
    while (retryCount < CONFIG.MAX_RETRY) {
        try {
            // 尝试点击返回按钮
            var backSelectors = [
                function() { return desc('返回').findOne(1000); },
                function() { return text('返回').findOne(1000); },
                function() { return className('android.widget.ImageView').desc('返回').findOne(1000); }
            ];

            var backSuccess = false;
            for (var i = 0; i < backSelectors.length; i++) {
                if (safeClick(backSelectors[i], '返回按钮 (方式' + (i + 1) + ')')) {
                    backSuccess = true;
                    break;
                }
            }

            // 如果没有找到返回按钮，使用系统返回键
            if (!backSuccess) {
                back();
                console.log('🔙 使用系统返回键');
            }

            waitFor(CONFIG.WAIT_MEDIUM);

            // 检查是否成功返回
            if (currentPackage() === CONFIG.APP_PACKAGE && !isInNewsDetail()) {
                console.log('✅ 成功返回上一页');
                return true;
            }

            retryCount++;
            console.log('⚠️ 返回重试 ' + retryCount + '/' + CONFIG.MAX_RETRY);

        } catch (e) {
            console.log('❌ 返回失败: ' + e.message);
            retryCount++;
        }
    }

    console.log('❌ 返回失败，已达到最大重试次数');
    return false;
}

// 下拉刷新
function pullToRefresh() {
    console.log('🔄 执行下拉刷新...');

    try {
        // 确保在列表顶部
        for (var i = 0; i < 3; i++) {
            safeSwipe(
                device.width / 2,
                device.height * 0.5,
                device.width / 2,
                device.height * 0.9,
                500,
                '滑动到顶部'
            );
            waitFor(500);
        }

        // 执行下拉刷新
        safeSwipe(
            device.width / 2,
            device.height * 0.3,
            device.width / 2,
            device.height * 0.8,
            1000,
            '下拉刷新'
        );

        waitFor(CONFIG.WAIT_LONG);
        console.log('✅ 下拉刷新完成');
        return true;
    } catch (e) {
        console.log('❌ 下拉刷新失败: ' + e.message);
        return false;
    }
}

// 点击热榜
function clickHotList() {
    console.log('尝试点击热榜...');
    
    var hotSelectors = [
        function() { return text('热榜').findOne(1000); },
        function() { return text('热点').findOne(1000); },
        function() { return desc('热榜').findOne(1000); },
        function() { return desc('热点').findOne(1000); }
    ];
    
    for (var i = 0; i < hotSelectors.length; i++) {
        if (safeClick(hotSelectors[i], '热榜按钮 (方式' + (i + 1) + ')')) {
            waitFor(2000);
            browseHotList();
            return;
        }
    }
    
    console.log('✗ 未找到热榜按钮');
}

// 浏览热榜内容
function browseHotList() {
    console.log('浏览热榜内容...');
    
    for (var i = 0; i < 3; i++) {
        safeSwipe(
            device.width / 2,
            device.height * 0.8,
            device.width / 2,
            device.height * 0.3,
            800,
            '浏览热榜 - 滑动 ' + (i + 1)
        );
        waitFor(2000);
    }
    
    console.log('✓ 热榜浏览完成');
}

// 主执行函数
function main() {
    var startTime = new Date().getTime();

    try {
        console.log('🚀 === 开始执行今日头条浏览脚本 ===');
        console.log('📱 设备信息: ' + device.width + 'x' + device.height);

        // 检查无障碍权限
        if (!auto.service) {
            console.log('❌ 无障碍服务未启用，请先启用无障碍服务');
            console.log('💡 请到设置 -> 无障碍 -> 启用本应用的无障碍服务');
            return false;
        }

        console.log('✅ 无障碍服务已启用');

        // 启动应用
        if (!launchToutiao()) {
            console.log('❌ 应用启动失败，脚本终止');
            return false;
        }

        // 等待应用完全加载
        waitFor(CONFIG.WAIT_LONG);
        handlePopups();

        // 下拉刷新
        if (pullToRefresh()) {
            console.log('✅ 刷新成功');
        }

        // 浏览新闻列表
        browseNewsList();

        // 点击热榜
        clickHotList();

        // 再次浏览新闻
        browseNewsList();

        var endTime = new Date().getTime();
        var duration = Math.round((endTime - startTime) / 1000);

        console.log('🎉 === 今日头条浏览脚本执行完成 ===');
        console.log('⏱️ 总耗时: ' + duration + '秒');

        return true;

    } catch (e) {
        console.log('❌ 脚本执行出错: ' + e.message);
        console.log('📋 错误堆栈: ' + e.stack);
        return false;
    }
}

// 执行主函数
main();

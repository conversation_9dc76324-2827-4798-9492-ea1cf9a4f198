<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/app_background_color"
    android:padding="16dp">

    <TextView
        android:id="@+id/debugTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Debug Options"
        android:textColor="@color/text_primary"
        android:textSize="18sp"
        android:textStyle="bold"
        android:padding="8dp"
        android:background="@color/header_background"
        android:elevation="4dp"
        android:layout_alignParentTop="true"/>

    <Button
        android:id="@+id/btnTestScript"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:backgroundTint="@color/button_primary"
        android:textColor="@color/white"
        android:elevation="4dp"
        android:padding="12dp"
        android:textSize="16sp"
        android:text="测试 AutoJS 功能"
        android:layout_below="@id/debugTitle"
        android:layout_marginTop="20dp"/>

    <Button
        android:id="@+id/btnToutiaoScript"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:backgroundTint="@color/button_primary"
        android:textColor="@color/white"
        android:elevation="4dp"
        android:padding="12dp"
        android:textSize="16sp"
        android:text="运行今日头条浏览脚本"
        android:layout_below="@id/btnTestScript"
        android:layout_marginTop="16dp"/>

    <Button
        android:id="@+id/btnStopSocket"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:backgroundTint="@color/button_primary"
        android:textColor="@color/white"
        android:elevation="4dp"
        android:padding="12dp"
        android:textSize="16sp"
        android:text="Stop All Services"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="86dp"/>
        
</RelativeLayout>
<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="JavaScript 脚本执行器"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:gravity="center"
            android:layout_marginBottom="16dp" />

        <!-- 状态信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/rounded_background"
            android:padding="12dp"
            android:layout_marginBottom="16dp">

            <TextView
                android:id="@+id/tvScriptStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="JS引擎状态检查中..."
                android:textSize="14sp"
                android:textColor="@android:color/black"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/tvRunningScripts"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="运行中的脚本: 0"
                android:textSize="14sp"
                android:textColor="@android:color/black" />

        </LinearLayout>

        <!-- 脚本名称输入 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="脚本名称"
            android:layout_marginBottom="16dp"
            app:boxStrokeColor="@color/colorPrimary"
            app:hintTextColor="@android:color/black">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etScriptName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@android:color/black"
                android:textColorHint="@android:color/darker_gray"
                android:singleLine="true" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 脚本内容输入 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="JavaScript脚本内容"
            android:layout_marginBottom="16dp"
            app:boxStrokeColor="@color/colorPrimary"
            app:hintTextColor="@android:color/black">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etScriptContent"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:gravity="top|start"
                android:inputType="textMultiLine"
                android:scrollbars="vertical"
                android:fontFamily="monospace"
                android:textSize="12sp"
                android:textColor="@android:color/black"
                android:textColorHint="@android:color/darker_gray" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 快速测试按钮 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="快速测试："
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="8dp" />

        <!-- 第一行：基础测试 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/btnTestBasic"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="基础测试"
                android:textColor="@android:color/black"
                android:background="#E8F5E8"
                android:layout_marginEnd="4dp"
                android:padding="8dp" />

            <Button
                android:id="@+id/btnTestIntermediate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="中级测试"
                android:textColor="@android:color/black"
                android:background="#FFF3E0"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:padding="8dp" />

            <Button
                android:id="@+id/btnTestAdvanced"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="高级测试"
                android:textColor="@android:color/black"
                android:background="#FFEBEE"
                android:layout_marginStart="4dp"
                android:padding="8dp" />

        </LinearLayout>

        <!-- 第二行：API测试 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/btnTestConsoleAPI"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Console API"
                android:textColor="@android:color/black"
                android:background="#E3F2FD"
                android:layout_marginEnd="4dp"
                android:padding="8dp" />

            <Button
                android:id="@+id/btnTestUtilsAPI"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Utils API"
                android:textColor="@android:color/black"
                android:background="#F3E5F5"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:padding="8dp" />

            <Button
                android:id="@+id/btnTestSystemAPI"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="System API"
                android:textColor="@android:color/black"
                android:background="#ECEFF1"
                android:layout_marginStart="4dp"
                android:padding="8dp" />

        </LinearLayout>

        <!-- 第三行：完整测试 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/btnTestAllAPIs"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="全部API测试"
                android:textColor="@android:color/black"
                android:background="#EFEBE9"
                android:layout_marginEnd="4dp"
                android:padding="8dp" />

            <Button
                android:id="@+id/btnTestPerformance"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="性能测试"
                android:textColor="@android:color/black"
                android:background="#FBE9E7"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:padding="8dp" />

            <Button
                android:id="@+id/btnTestStress"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="压力测试"
                android:textColor="@android:color/black"
                android:background="#FCE4EC"
                android:layout_marginStart="4dp"
                android:padding="8dp" />

        </LinearLayout>

        <!-- 实用脚本 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="实用脚本："
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/btnToutiaoScript"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="今日头条浏览"
                android:textColor="@android:color/white"
                android:background="#FF5722"
                android:layout_marginEnd="4dp"
                android:padding="12dp" />

            <Button
                android:id="@+id/btnTestAutoJS"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="AutoJS功能测试"
                android:textColor="@android:color/white"
                android:background="#2196F3"
                android:layout_marginStart="4dp"
                android:padding="12dp" />

        </LinearLayout>

        <!-- 控制按钮 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="脚本控制："
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/btnExecuteScript"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="执行脚本"
                android:textColor="@android:color/black"
                android:background="#CCCCCC"
                android:layout_marginBottom="8dp"
                android:padding="12dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnStopAllScripts"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="停止所有脚本"
                    android:textColor="@android:color/black"
                    android:background="#CCCCCC"
                    android:layout_marginEnd="8dp"
                    android:padding="12dp" />

                <Button
                    android:id="@+id/btnCheckStatus"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="检查状态"
                    android:textColor="@android:color/black"
                    android:background="#CCCCCC"
                    android:padding="12dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 使用说明 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="使用说明："
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="1. 在上方文本框中输入JavaScript脚本代码\n2. 点击'执行脚本'运行脚本\n3. 使用'停止所有脚本'可以终止正在运行的脚本\n4. 点击'检查状态'查看JS引擎运行状态\n5. 脚本可以使用基本的API，如utils.log()、console.log()、utils.sleep()等\n\n注意：这是简化版本的JS执行器，主要用于基本的脚本测试和学习"
            android:textSize="14sp"
            android:textColor="@android:color/black"
            android:lineSpacingExtra="4dp"
            android:background="@drawable/rounded_background_light"
            android:padding="12dp" />

    </LinearLayout>

</ScrollView>

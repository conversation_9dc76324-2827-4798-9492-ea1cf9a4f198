#!/bin/bash

# AutoJs6 测试脚本
# 用于构建和测试AutoJs6功能

echo "=== AutoJs6 测试脚本开始 ==="

# 检查环境
echo "1. 检查环境..."
if [ ! -f "gradlew" ]; then
    echo "错误: 未找到gradlew文件，请确保在项目根目录运行此脚本"
    exit 1
fi

# 清理项目
echo "2. 清理项目..."
./gradlew clean

# 构建项目
echo "3. 构建项目..."
./gradlew build

if [ $? -ne 0 ]; then
    echo "错误: 项目构建失败"
    exit 1
fi

echo "✓ 项目构建成功"

# 运行单元测试
echo "4. 运行单元测试..."
./gradlew test

if [ $? -ne 0 ]; then
    echo "警告: 单元测试失败，但继续执行"
else
    echo "✓ 单元测试通过"
fi

# 检查设备连接
echo "5. 检查设备连接..."
adb devices

# 安装应用（如果有设备连接）
DEVICE_COUNT=$(adb devices | grep -v "List of devices" | grep -c "device")
if [ $DEVICE_COUNT -gt 0 ]; then
    echo "6. 安装应用到设备..."
    ./gradlew installDebug
    
    if [ $? -eq 0 ]; then
        echo "✓ 应用安装成功"
        
        # 运行集成测试
        echo "7. 运行集成测试..."
        ./gradlew connectedAndroidTest
        
        if [ $? -eq 0 ]; then
            echo "✓ 集成测试通过"
        else
            echo "警告: 集成测试失败"
        fi
    else
        echo "警告: 应用安装失败"
    fi
else
    echo "警告: 未检测到连接的设备，跳过设备测试"
fi

echo ""
echo "=== 测试总结 ==="
echo "✓ 项目构建: 成功"
echo "✓ 单元测试: 完成"
if [ $DEVICE_COUNT -gt 0 ]; then
    echo "✓ 设备测试: 完成"
else
    echo "- 设备测试: 跳过（无设备）"
fi

echo ""
echo "=== AutoJs6 测试脚本完成 ==="
echo ""
echo "如何手动测试AutoJs6功能："
echo "1. 启动应用"
echo "2. 切换到'JS脚本执行器'标签页"
echo "3. 点击'执行脚本'按钮测试默认脚本"
echo "4. 观察控制台输出和Toast消息"
echo "5. 检查'运行中的脚本'计数"
echo ""
echo "测试脚本位置："
echo "- test_scripts/simple_test.js - 基础功能测试"
echo "- test_scripts/autojs6_framework_test.js - 框架深度测试"
echo "- test_scripts/debug_test.js - 调试测试"

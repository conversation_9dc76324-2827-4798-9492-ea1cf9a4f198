// 调试测试脚本
console.log("=== 开始调试测试 ===");

// 测试1: 基本console.log
console.log("测试1: console.log 正常工作");
if (typeof utils !== 'undefined') {
    utils.toast("🐛 调试测试: console.log 正常");
}

// 测试2: 检查utils对象是否存在
console.log("测试2: 检查utils对象");
if (typeof utils !== 'undefined') {
    console.log("utils对象存在");
    
    // 测试utils.log
    try {
        utils.log("测试3: utils.log 正常工作");
        console.log("utils.log 调用成功");
        utils.toast("📝 utils.log: 正常工作");
    } catch (e) {
        console.log("utils.log 调用失败: " + e);
        utils.toast("❌ utils.log: 调用失败");
    }
    
    // 测试utils.toast
    try {
        utils.toast("🔔 测试4: utils.toast 正常工作");
        console.log("utils.toast 调用成功");
    } catch (e) {
        console.log("utils.toast 调用失败: " + e);
        utils.toast("❌ utils.toast: 调用失败");
    }
    
} else {
    console.log("utils对象不存在");
}

// 测试5: 基本JavaScript功能
console.log("测试5: 基本JavaScript功能");
var a = 10;
var b = 20;
var result = a + b;
console.log("计算结果: " + a + " + " + b + " = " + result);
if (typeof utils !== 'undefined') {
    utils.toast("🧮 基本计算: " + a + " + " + b + " = " + result);
}

// 测试6: 函数定义
console.log("测试6: 函数定义");
function testFunction() {
    return "函数调用成功";
}
console.log(testFunction());
if (typeof utils !== 'undefined') {
    utils.toast("⚙️ 函数测试: " + testFunction());
}

// 测试7: 对象操作
console.log("测试7: 对象操作");
var obj = {
    name: "测试对象",
    value: 42
};
console.log("对象属性: " + obj.name + ", 值: " + obj.value);
if (typeof utils !== 'undefined') {
    utils.toast("📦 对象操作: " + obj.name + " = " + obj.value);
}

console.log("=== 调试测试完成 ===");
if (typeof utils !== 'undefined') {
    utils.toast("🎉 调试测试: 全部完成");
}
"所有测试完成";

<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2022 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
-->
<animated-vector xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:aapt="http://schemas.android.com/aapt"
  xmlns:tools="http://schemas.android.com/tools"
  android:drawable="@drawable/mtrl_ic_check_mark"
  tools:ignore="NewApi">
  <target android:name="@string/mtrl_checkbox_button_icon_path_group_name">
    <aapt:attr name="android:animation">
      <objectAnimator
        android:duration="@integer/m3_sys_motion_duration_short3"
        android:interpolator="?attr/motionEasingEmphasizedAccelerateInterpolator"
        android:propertyName="scaleX"
        android:valueFrom="1.0"
        android:valueTo="0.6"
        android:valueType="floatType"/>
    </aapt:attr>
  </target>
  <target android:name="@string/mtrl_checkbox_button_icon_path_group_name">
    <aapt:attr name="android:animation">
      <objectAnimator
        android:duration="@integer/m3_sys_motion_duration_short3"
        android:interpolator="?attr/motionEasingEmphasizedAccelerateInterpolator"
        android:propertyName="scaleY"
        android:valueFrom="1.0"
        android:valueTo="0.6"
        android:valueType="floatType"/>
    </aapt:attr>
  </target>
  <target android:name="@string/mtrl_checkbox_button_icon_path_name">
    <aapt:attr name="android:animation">
      <objectAnimator
        android:duration="@integer/m3_sys_motion_duration_short1"
        android:interpolator="?attr/motionEasingLinearInterpolator"
        android:propertyName="fillAlpha"
        android:valueFrom="1.0"
        android:valueTo="0.0"
        android:valueType="floatType"/>
    </aapt:attr>
  </target>
</animated-vector>

[{"merged": "com.bm.atool.app-mergeDebugResources-44:/layout/fragment_debug.xml", "source": "com.bm.atool.app-main-47:/layout/fragment_debug.xml"}, {"merged": "com.bm.atool.app-mergeDebugResources-44:/layout/fragment_simple_autojs.xml", "source": "com.bm.atool.app-main-47:/layout/fragment_simple_autojs.xml"}, {"merged": "com.bm.atool.app-mergeDebugResources-44:/layout/setting_row.xml", "source": "com.bm.atool.app-main-47:/layout/setting_row.xml"}, {"merged": "com.bm.atool.app-mergeDebugResources-44:/layout/activity_login.xml", "source": "com.bm.atool.app-main-47:/layout/activity_login.xml"}, {"merged": "com.bm.atool.app-mergeDebugResources-44:/layout/phone_row.xml", "source": "com.bm.atool.app-main-47:/layout/phone_row.xml"}, {"merged": "com.bm.atool.app-mergeDebugResources-44:/layout/fragment_autojs.xml", "source": "com.bm.atool.app-main-47:/layout/fragment_autojs.xml"}, {"merged": "com.bm.atool.app-mergeDebugResources-44:/layout/activity_main.xml", "source": "com.bm.atool.app-main-47:/layout/activity_main.xml"}, {"merged": "com.bm.atool.app-mergeDebugResources-44:/layout/sms_row.xml", "source": "com.bm.atool.app-main-47:/layout/sms_row.xml"}, {"merged": "com.bm.atool.app-mergeDebugResources-44:/layout/fragment_settings.xml", "source": "com.bm.atool.app-main-47:/layout/fragment_settings.xml"}, {"merged": "com.bm.atool.app-mergeDebugResources-44:/layout/activity_float_item.xml", "source": "com.bm.atool.app-main-47:/layout/activity_float_item.xml"}, {"merged": "com.bm.atool.app-mergeDebugResources-44:/layout/fragment_main.xml", "source": "com.bm.atool.app-main-47:/layout/fragment_main.xml"}]
# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a comprehensive Android application called "android-tool" that integrates SMS management, socket communication, and JavaScript automation capabilities through a simplified AutoJs6 module. The project follows a modular architecture with multiple components and uses a multi-process architecture for stability.

## Build Commands

### Core Build Commands
```bash
# Clean and build the entire project
./gradlew clean build

# Build debug APK
./gradlew assembleDebug

# Build release APK
./gradlew assembleRelease

# Install debug build to connected device
./gradlew installDebug
```

### Testing Commands
```bash
# Run unit tests
./gradlew test

# Run instrumentation tests on connected devices
./gradlew connectedAndroidTest

# Run all checks (lint, unit tests, etc.)
./gradlew check

# Run lint checks
./gradlew lint

# Apply automatic lint fixes
./gradlew lintFix
```

### Testing Scripts
```bash
# Windows: Comprehensive AutoJs6 testing
test_autojs6.bat

# Linux/Mac: Comprehensive AutoJs6 testing  
./test_autojs6.sh

# Stress testing for socket connectivity
./run_stress_test.sh

# Run specific stress test class
./gradlew connectedAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=com.bm.atool.StressTest
```

## Architecture Overview

### Multi-Process Architecture
The application uses multiple processes for stability:
- `:main` - Main application process
- `:socket` - Socket communication service
- `:watch_dog` - Process monitoring
- `:watch_player` - Media playback service
- `:accessibility` - Accessibility service
- `:watch_job` - Job scheduling

### Core Modules

#### Main Application (`app/`)
- **Package**: `com.bm.atool`
- **Target SDK**: 35 (Android 14)
- **Min SDK**: 24 (Android 7.0)
- **Key Features**: SMS management, socket communication, JavaScript automation

#### AutoJs6 Module (`autojs6-module/`)
- **Package**: `com.bm.autojs6.module`
- **Purpose**: Simplified JavaScript execution engine using Rhino 1.7.13
- **Dependencies**: Rhino 1.7.13, Android libraries
- **Status**: Fully functional and stable (current implementation)

#### AutoJs6 Framework (Legacy)
- **Status**: Implemented but not integrated due to dependency conflicts
- **Location**: `app/src/main/java/com/bm/atool/autojs/`
- **Features**: Advanced process management, script execution, API injection (designed but not active)

### Key Components

#### AutoJs6 Integration (Current)
- **AutoJs6Module**: Simplified module management with singleton pattern
- **ScriptEngine**: Rhino 1.7.13-based JavaScript execution engine with string-based API injection
- **SimpleAutoJsFragment**: UI for script editing and execution with built-in test scripts

#### AutoJs6 Integration (Legacy - Not Active)
- **AutoJsFramework**: Central framework management with advanced process restart prevention
- **AutoJs6Engine**: More comprehensive JavaScript execution engine (dependency conflicts)
- **Full API Bridge**: Complete Android API exposure system

#### SMS Management
- SMS send/receive capabilities
- USSD code processing
- SIM state monitoring
- SMS synchronization and acknowledgment

#### Socket Communication
- Real-time network communication with heartbeat mechanism
- Connection monitoring and auto-reconnection
- Pre-initialization for fast connections
- Multi-process socket services

#### System Services
- **WatchDogService**: Process monitoring and recovery
- **ANTAccessibilityService**: UI automation capabilities
- **NotificationService**: Notification listening
- **JobSchedulerService**: Scheduled task execution

## Security and Permissions

### Critical Permissions
```xml
<!-- SMS & Telephony -->
READ_SMS, SEND_SMS, WRITE_SMS
READ_PHONE_STATE, READ_PRIVILEGED_PHONE_STATE

<!-- System & Network -->
INTERNET, ACCESS_NETWORK_STATE
WAKE_LOCK, FOREGROUND_SERVICE

<!-- Special Permissions -->
BIND_ACCESSIBILITY_SERVICE
SYSTEM_ALERT_WINDOW
MANAGE_EXTERNAL_STORAGE
```

### Security Features
- String obfuscation using StringFog plugin
- Permission validation framework
- Script sandboxing in WebView
- API call monitoring and validation

## JavaScript Automation

### Available APIs (Complete)
```javascript
// Console API
console.log(message)
console.error(message)
console.warn(message)
console.info(message)
console.debug(message)
console.dir(object)

// Utils API
utils.log(message)
utils.sleep(milliseconds)
utils.toast(message)
utils.currentPackage()

// Android API
android.context.getPackageName()
android.device.brand
android.device.model
android.device.width
android.device.height
```

### Architecture Pattern (Current)
```
Android Application
    ↓
AutoJs6Module (Simplified Module Management)
    ↓
ScriptEngine (Rhino 1.7.13 Execution Engine)
    ↓
Rhino JavaScript Runtime
    ↓
String-based API Injection
    ↓
Android Native APIs
```

### Architecture Pattern (Legacy - Not Active)
```
Android Application
    ↓
AutoJsFramework (Process Management & Script Control)
    ↓
AutoJs6Engine (Rhino 1.7.13 Execution Engine)
    ↓
Rhino JavaScript Runtime
    ↓
API Bridge (Object-based Definition)
    ↓
Android Native APIs
```

## Testing Infrastructure

### Test Structure
- **Unit Tests**: `app/src/test/java/`
- **Instrumentation Tests**: `app/src/androidTest/java/`
- **Test Scripts**: `test_scripts/` directory

### Key Test Files
- `simple_test.js` - Basic JavaScript functionality and API testing
- `autojs6_framework_test.js` - Comprehensive framework integration tests
- `debug_test.js` - Debugging utilities and error handling
- `enhanced_test.js` - Advanced JavaScript features testing
- `js_execution_test.js` - JavaScript engine execution tests
- `StressTest.java` - Socket connectivity stress testing
- `fix_verification_test.js` - Bug fix verification tests

### Testing Approach
- JUnit 4 for unit testing
- AndroidX Test for instrumentation tests
- Custom stress testing framework
- Automated test execution via Gradle

## Development Workflow

### Adding New JavaScript APIs
1. Add new API injection code in `ScriptEngine.injectAndroidAPIs()` method
2. Use string-based JavaScript object definition for compatibility
3. Implement functionality using Android APIs in the injection script
4. Test with existing test scripts to ensure compatibility

### Modifying UI Components
1. Update layout files in `app/src/main/res/layout/`
2. Update view references in corresponding Fragment/Activity classes
3. Add new event handlers if needed

### Extending Script Management
1. Modify `AutoJs6Module` class for new management features
2. Update `ScriptEngine` for new execution capabilities
3. Add new tracking or monitoring capabilities in script models

## Current Limitations

### Disabled Features
- Full AutoJs6 framework integration (dependency conflicts with javax.lang.model)
- Complex object-based API injection system
- Advanced accessibility service features
- Comprehensive security management system

### Simplified AutoJs6 Module Limitations
- String-based API injection (less flexible than object-based)
- No accessibility service integration
- No screen interaction capabilities (clicks, swipes)
- No image recognition or OCR
- No file system operations
- No network request functionality
- Limited Android API exposure (basic device info only)
- Performance overhead from Rhino interpretation mode

## Troubleshooting

### Common Issues
1. **"AutoJs6Module not initialized"** - Call `AutoJs6Module.getInstance().initialize(context)` first
2. **Script execution errors** - Check Android logs with tag "ScriptEngine" for detailed error messages
3. **API injection failures** - Verify string-based JavaScript definitions in `ScriptEngine.injectAndroidAPIs()`
4. **Rhino compatibility issues** - Ensure using Rhino 1.7.13 to avoid javax.lang.model dependency conflicts
5. **Socket connection issues** - Check network connectivity and service status

### Debugging Approach
1. Check Android logs with tags "AutoJs6Module", "ScriptEngine", and "AutoJsFramework"
2. Monitor JavaScript console output via `console.log()` calls in test scripts
3. Verify script content formatting and syntax
4. Use test scripts to validate API availability and functionality
5. Check initialization status with `AutoJs6Module.getInstance().isInitialized()`
6. Use stress testing scripts to verify socket connectivity

## Build Configuration

### Key Dependencies
```gradle
// Core Android
androidx.appcompat:appcompat:1.7.0
com.google.android.material:material:1.5.0

// JavaScript Engine (Critical: Must use 1.7.13 to avoid dependency conflicts)
org.mozilla:rhino:1.7.13

// Network & Communication
com.squareup.retrofit2:retrofit:2.11.0
io.socket:socket.io-client:2.1.1

// Security & Obfuscation
com.github.megatronking.stringfog:xor:5.0.0
com.github.megatronking.stringfog:gradle-plugin:5.2.0

// MultiDex Support
androidx.multidex:multidex:2.0.1

// Core Library Desugaring
com.android.tools:desugar_jdk_libs:2.0.4
```

### Build Features
- **View Binding**: Enabled for type-safe view access
- **Build Config**: Enabled for build-time configuration
- **String Obfuscation**: Enabled via StringFog plugin for code protection
- **MultiDex**: Enabled for large applications with many dependencies
- **Core Library Desugaring**: Enabled for modern Java features on older devices
- **Kotlin Support**: Enabled with Kotlin 1.9.10

### Critical Build Notes
- **Rhino Version**: Must use 1.7.13 to avoid javax.lang.model dependency conflicts
- **StringFog**: Configured with XOR encryption and random key generation
- **Multi-Process**: Uses separate processes for socket, watchdog, and accessibility services

## File Structure
```
android-tool-v2/
├── app/                           # Main application module
│   ├── src/main/java/com/bm/atool/
│   │   ├── autojs/               # AutoJs6 framework (legacy, not integrated)
│   │   │   ├── api/              # API bridge components
│   │   │   ├── engine/           # Execution engines
│   │   │   ├── manager/          # Script management
│   │   │   ├── model/            # Data models
│   │   │   ├── security/         # Security components
│   │   │   └── storage/          # Script storage
│   │   ├── services/             # Background services
│   │   │   ├── socket/           # Socket communication
│   │   │   ├── accessibility/    # Accessibility services
│   │   │   └── watchdog/         # Process monitoring
│   │   ├── ui/                   # UI components
│   │   │   ├── fragments/        # Main UI fragments
│   │   │   └── adapters/         # Data adapters
│   │   └── utils/                # Utility classes
│   └── src/main/res/
│       ├── layout/               # Layout files
│       ├── values/               # Resources
│       └── AndroidManifest.xml   # App manifest
├── autojs6-module/               # Active AutoJs6 module
│   ├── src/main/java/com/bm/autojs6/module/
│   │   ├── engine/               # Script engine implementation
│   │   └── model/                # Script data models
│   └── build.gradle              # Module build configuration
├── test_scripts/                 # JavaScript test scripts
│   ├── simple_test.js            # Basic functionality tests
│   ├── autojs6_framework_test.js # Comprehensive framework tests
│   ├── debug_test.js             # Debug utilities
│   └── enhanced_test.js          # Advanced features tests
├── gradle/                       # Gradle wrapper
├── build.gradle                  # Root build configuration
├── settings.gradle               # Project settings
├── test_autojs6.sh/.bat          # Test automation scripts
└── run_stress_test.sh            # Socket stress testing
```

## Current Implementation Status

### Active Components
- **AutoJs6Module**: Fully functional simplified JavaScript execution module
- **ScriptEngine**: Rhino 1.7.13-based execution with string-based API injection
- **SimpleAutoJsFragment**: Complete UI integration with test scripts
- **SMS Management**: Working SMS send/receive with USSD support
- **Socket Communication**: Multi-process socket services with heartbeat
- **Test Infrastructure**: Comprehensive test scripts and automation

### Legacy Components (Not Active)
- **AutoJsFramework**: Complete framework implementation (dependency conflicts)
- **Full API Bridge**: Object-based API injection system
- **Advanced Security Management**: Comprehensive sandboxing and validation

### Known Issues
- **Dependency Conflict**: Full AutoJs6 framework conflicts with javax.lang.model
- **API Limitations**: String-based injection is less flexible than object-based
- **Performance**: Rhino interpretation mode has overhead compared to compiled mode

### Future Development
1. **Resolve Dependencies**: Update to newer Rhino version or resolve conflicts
2. **Enhanced APIs**: Add more Android API exposure through string-based injection
3. **Performance Optimization**: Explore compiled mode or alternative JavaScript engines
4. **Security Enhancement**: Implement more robust script sandboxing
5. **File System Integration**: Add safe file operations to JavaScript environment
#Tue Aug 19 23:18:16 CST 2025
base.0=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\10\\classes.dex
base.10=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\1\\classes.dex
base.11=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\2\\classes.dex
base.12=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\3\\classes.dex
base.13=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\5\\classes.dex
base.14=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\6\\classes.dex
base.15=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\7\\classes.dex
base.16=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\8\\classes.dex
base.17=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\desugar_lib_dex\\debug\\classes1000.dex
base.18=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
base.2=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\14\\classes.dex
base.3=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\8\\classes.dex
base.4=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.5=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\10\\classes.dex
base.6=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\12\\classes.dex
base.7=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\13\\classes.dex
base.8=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\14\\classes.dex
base.9=F\:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\15\\classes.dex
path.0=classes.dex
path.1=10/classes.dex
path.10=1/classes.dex
path.11=2/classes.dex
path.12=3/classes.dex
path.13=5/classes.dex
path.14=6/classes.dex
path.15=7/classes.dex
path.16=8/classes.dex
path.17=classes1000.dex
path.18=classes2.dex
path.2=14/classes.dex
path.3=8/classes.dex
path.4=0/classes.dex
path.5=10/classes.dex
path.6=12/classes.dex
path.7=13/classes.dex
path.8=14/classes.dex
path.9=15/classes.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.10=classes11.dex
renamed.11=classes12.dex
renamed.12=classes13.dex
renamed.13=classes14.dex
renamed.14=classes15.dex
renamed.15=classes16.dex
renamed.16=classes17.dex
renamed.17=classes18.dex
renamed.18=classes19.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
renamed.8=classes9.dex
renamed.9=classes10.dex

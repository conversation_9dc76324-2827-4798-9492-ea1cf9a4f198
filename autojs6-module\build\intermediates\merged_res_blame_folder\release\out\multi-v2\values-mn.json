{"logs": [{"outputFile": "com.bm.autojs6.module.autojs6-module-merged_res-30:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7c30190ed39ed0daaeac1a7b87972a79\\transformed\\core-1.12.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3414,3512,3614,3715,3813,3918,4030,9305", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "3507,3609,3710,3808,3913,4025,4144,9401"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\40d8a17e80b34dfeac4d4324ceeb8814\\transformed\\material-1.11.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,500,587,672,770,889,974,1039,1137,1218,1277,1370,1432,1495,1553,1624,1686,1740,1861,1918,1979,2033,2104,2237,2321,2404,2537,2619,2697,2829,2919,2999,3053,3104,3170,3241,3319,3405,3484,3559,3637,3717,3800,3905,3993,4072,4162,4255,4329,4399,4490,4544,4624,4691,4775,4860,4922,4986,5049,5120,5224,5339,5436,5550,5608,5663", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,78,76,78,86,84,97,118,84,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,82,132,81,77,131,89,79,53,50,65,70,77,85,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,114,96,113,57,54,83", "endOffsets": "260,339,416,495,582,667,765,884,969,1034,1132,1213,1272,1365,1427,1490,1548,1619,1681,1735,1856,1913,1974,2028,2099,2232,2316,2399,2532,2614,2692,2824,2914,2994,3048,3099,3165,3236,3314,3400,3479,3554,3632,3712,3795,3900,3988,4067,4157,4250,4324,4394,4485,4539,4619,4686,4770,4855,4917,4981,5044,5115,5219,5334,5431,5545,5603,5658,5742"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3086,3163,3242,3329,4149,4247,4366,4451,4516,4614,4695,4754,4847,4909,4972,5030,5101,5163,5217,5338,5395,5456,5510,5581,5714,5798,5881,6014,6096,6174,6306,6396,6476,6530,6581,6647,6718,6796,6882,6961,7036,7114,7194,7277,7382,7470,7549,7639,7732,7806,7876,7967,8021,8101,8168,8252,8337,8399,8463,8526,8597,8701,8816,8913,9027,9085,9140", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,78,76,78,86,84,97,118,84,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,82,132,81,77,131,89,79,53,50,65,70,77,85,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,114,96,113,57,54,83", "endOffsets": "310,3081,3158,3237,3324,3409,4242,4361,4446,4511,4609,4690,4749,4842,4904,4967,5025,5096,5158,5212,5333,5390,5451,5505,5576,5709,5793,5876,6009,6091,6169,6301,6391,6471,6525,6576,6642,6713,6791,6877,6956,7031,7109,7189,7272,7377,7465,7544,7634,7727,7801,7871,7962,8016,8096,8163,8247,8332,8394,8458,8521,8592,8696,8811,8908,9022,9080,9135,9219"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ba2a91daeaa12d6af754138dd647fd8a\\transformed\\appcompat-1.6.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,529,638,724,830,944,1027,1108,1199,1292,1387,1483,1580,1673,1767,1859,1950,2040,2120,2227,2330,2427,2534,2636,2749,2908,9224", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "424,524,633,719,825,939,1022,1103,1194,1287,1382,1478,1575,1668,1762,1854,1945,2035,2115,2222,2325,2422,2529,2631,2744,2903,3002,9300"}}]}, {"outputFile": "com.bm.autojs6.module.autojs6-module-mergeReleaseResources-28:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7c30190ed39ed0daaeac1a7b87972a79\\transformed\\core-1.12.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3414,3512,3614,3715,3813,3918,4030,9305", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "3507,3609,3710,3808,3913,4025,4144,9401"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\40d8a17e80b34dfeac4d4324ceeb8814\\transformed\\material-1.11.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,500,587,672,770,889,974,1039,1137,1218,1277,1370,1432,1495,1553,1624,1686,1740,1861,1918,1979,2033,2104,2237,2321,2404,2537,2619,2697,2829,2919,2999,3053,3104,3170,3241,3319,3405,3484,3559,3637,3717,3800,3905,3993,4072,4162,4255,4329,4399,4490,4544,4624,4691,4775,4860,4922,4986,5049,5120,5224,5339,5436,5550,5608,5663", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,78,76,78,86,84,97,118,84,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,82,132,81,77,131,89,79,53,50,65,70,77,85,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,114,96,113,57,54,83", "endOffsets": "260,339,416,495,582,667,765,884,969,1034,1132,1213,1272,1365,1427,1490,1548,1619,1681,1735,1856,1913,1974,2028,2099,2232,2316,2399,2532,2614,2692,2824,2914,2994,3048,3099,3165,3236,3314,3400,3479,3554,3632,3712,3795,3900,3988,4067,4157,4250,4324,4394,4485,4539,4619,4686,4770,4855,4917,4981,5044,5115,5219,5334,5431,5545,5603,5658,5742"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3086,3163,3242,3329,4149,4247,4366,4451,4516,4614,4695,4754,4847,4909,4972,5030,5101,5163,5217,5338,5395,5456,5510,5581,5714,5798,5881,6014,6096,6174,6306,6396,6476,6530,6581,6647,6718,6796,6882,6961,7036,7114,7194,7277,7382,7470,7549,7639,7732,7806,7876,7967,8021,8101,8168,8252,8337,8399,8463,8526,8597,8701,8816,8913,9027,9085,9140", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,78,76,78,86,84,97,118,84,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,82,132,81,77,131,89,79,53,50,65,70,77,85,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,114,96,113,57,54,83", "endOffsets": "310,3081,3158,3237,3324,3409,4242,4361,4446,4511,4609,4690,4749,4842,4904,4967,5025,5096,5158,5212,5333,5390,5451,5505,5576,5709,5793,5876,6009,6091,6169,6301,6391,6471,6525,6576,6642,6713,6791,6877,6956,7031,7109,7189,7272,7377,7465,7544,7634,7727,7801,7871,7962,8016,8096,8163,8247,8332,8394,8458,8521,8592,8696,8811,8908,9022,9080,9135,9219"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ba2a91daeaa12d6af754138dd647fd8a\\transformed\\appcompat-1.6.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,529,638,724,830,944,1027,1108,1199,1292,1387,1483,1580,1673,1767,1859,1950,2040,2120,2227,2330,2427,2534,2636,2749,2908,9224", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "424,524,633,719,825,939,1022,1103,1194,1287,1382,1478,1575,1668,1762,1854,1945,2035,2115,2222,2325,2422,2529,2631,2744,2903,3002,9300"}}]}]}
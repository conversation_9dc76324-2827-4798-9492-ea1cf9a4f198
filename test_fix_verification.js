// 验证修复效果的测试脚本
console.log('=== 修复验证测试开始 ===');
console.log('测试时间: ' + new Date().toString());

// 1. 基础console.log测试
console.log('1. 基础console.log测试:');
console.log('Hello AutoJs6 Module!');
console.log('✓ console.log正常工作');

// 2. 变量和计算测试
console.log('2. 变量和计算测试:');
var a = 10;
var b = 20;
var result = a + b;
console.log('计算结果: ' + a + ' + ' + b + ' = ' + result);
console.log('✓ 基础运算正常');

// 3. 循环测试
console.log('3. 循环测试:');
for (var i = 1; i <= 3; i++) {
    console.log('  循环第 ' + i + ' 次');
}
console.log('✓ for循环正常');

// 4. 函数测试
console.log('4. 函数测试:');
function greet(name) {
    return 'Hello, ' + name + '!';
}
var greeting = greet('AutoJs6');
console.log(greeting);
console.log('✓ 函数定义和调用正常');

// 5. 对象测试
console.log('5. 对象测试:');
var person = {
    name: '张三',
    age: 25
};
console.log('姓名: ' + person.name + ', 年龄: ' + person.age);
console.log('✓ 对象操作正常');

// 6. 数组测试
console.log('6. 数组测试:');
var arr = [1, 2, 3, 4, 5];
console.log('数组: [' + arr.join(', ') + ']');
console.log('数组长度: ' + arr.length);
console.log('✓ 数组操作正常');

// 7. utils工具函数测试
console.log('7. Utils工具函数测试:');
if (typeof utils !== 'undefined') {
    utils.log('这是通过utils.log输出的消息');
    console.log('✓ utils对象可用');
} else {
    console.log('✗ utils对象未定义');
}

// 8. 日期对象测试
console.log('8. 日期对象测试:');
var now = new Date();
console.log('当前时间: ' + now.toString());
console.log('✓ Date对象正常');

// 9. 字符串操作测试
console.log('9. 字符串操作测试:');
var testStr = 'Hello World';
console.log('原字符串: ' + testStr);
console.log('大写: ' + testStr.toUpperCase());
console.log('长度: ' + testStr.length);
console.log('✓ 字符串操作正常');

// 10. 条件语句测试
console.log('10. 条件语句测试:');
var x = 10;
if (x > 5) {
    console.log('x大于5');
} else {
    console.log('x不大于5');
}
console.log('✓ 条件语句正常');

console.log('=== 修复验证测试完成 ===');
console.log('所有基础功能测试通过！');

'测试脚本执行成功 - 修复验证完成';

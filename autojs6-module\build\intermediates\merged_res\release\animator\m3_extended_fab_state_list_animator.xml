<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright 2019 The Android Open Source Project

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->

<set xmlns:android="http://schemas.android.com/apk/res/android">
  <selector>

    <!-- Pressed state -->
    <item
        android:state_enabled="true"
        android:state_pressed="true">
      <set>
        <objectAnimator
            android:duration="@integer/mtrl_btn_anim_duration_ms"
            android:propertyName="translationZ"
            android:valueTo="@dimen/m3_comp_extended_fab_primary_pressed_container_elevation"
            android:valueType="floatType"/>
        <objectAnimator
            android:duration="0"
            android:propertyName="elevation"
            android:valueTo="@dimen/m3_comp_extended_fab_primary_container_elevation"
            android:valueType="floatType"/>
      </set>
    </item>

    <!-- Hover state. This is triggered via mouse. -->
    <item
        android:state_enabled="true"
        android:state_hovered="true">
      <set>
        <objectAnimator
            android:duration="@integer/mtrl_btn_anim_duration_ms"
            android:propertyName="translationZ"
            android:valueTo="@dimen/m3_comp_extended_fab_primary_hover_container_elevation"
            android:valueType="floatType"/>
        <objectAnimator
            android:duration="0"
            android:propertyName="elevation"
            android:valueTo="@dimen/m3_comp_extended_fab_primary_container_elevation"
            android:valueType="floatType"/>
      </set>
    </item>

    <!-- Focused state. This is triggered via keyboard. -->
    <item
        android:state_enabled="true"
        android:state_focused="true">
      <set>
        <objectAnimator
            android:duration="@integer/mtrl_btn_anim_duration_ms"
            android:propertyName="translationZ"
            android:valueTo="@dimen/m3_comp_extended_fab_primary_focus_container_elevation"
            android:valueType="floatType"/>
        <objectAnimator
            android:duration="0"
            android:propertyName="elevation"
            android:valueTo="@dimen/m3_comp_extended_fab_primary_container_elevation"
            android:valueType="floatType"/>
      </set>
    </item>

    <!-- Base state (enabled, not pressed) -->
    <item android:state_enabled="true">
      <set>
        <objectAnimator
            android:duration="@integer/mtrl_btn_anim_duration_ms"
            android:propertyName="translationZ"
            android:startDelay="@integer/mtrl_btn_anim_delay_ms"
            android:valueTo="@dimen/mtrl_extended_fab_translation_z_base"
            android:valueType="floatType"/>
        <objectAnimator
            android:duration="0"
            android:propertyName="elevation"
            android:valueTo="@dimen/m3_comp_extended_fab_primary_container_elevation"
            android:valueType="floatType"/>
      </set>
    </item>

    <!-- Disabled state -->
    <item>
      <set>
        <objectAnimator
            android:duration="0"
            android:propertyName="translationZ"
            android:valueTo="@dimen/mtrl_extended_fab_disabled_translation_z"
            android:valueType="floatType"/>
        <objectAnimator
            android:duration="0"
            android:propertyName="elevation"
            android:valueTo="@dimen/mtrl_extended_fab_disabled_elevation"
            android:valueType="floatType"/>
      </set>
    </item>

  </selector>
</set>

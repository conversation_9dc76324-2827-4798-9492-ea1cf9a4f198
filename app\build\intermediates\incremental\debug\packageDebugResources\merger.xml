<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res"><file name="debug" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\debug.png" qualifiers="" type="drawable"/><file name="debug_select" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\debug_select.png" qualifiers="" type="drawable"/><file name="home" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\home.png" qualifiers="" type="drawable"/><file name="home_select" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\home_select.png" qualifiers="" type="drawable"/><file name="ic_code" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\ic_code.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="rounded_background" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\rounded_background.xml" qualifiers="" type="drawable"/><file name="rounded_background_light" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\rounded_background_light.xml" qualifiers="" type="drawable"/><file name="setting" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\setting.png" qualifiers="" type="drawable"/><file name="setting_select" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\setting_select.png" qualifiers="" type="drawable"/><file name="tab_icon_debug" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\tab_icon_debug.xml" qualifiers="" type="drawable"/><file name="tab_icon_home" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\tab_icon_home.xml" qualifiers="" type="drawable"/><file name="tab_icon_settings" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\tab_icon_settings.xml" qualifiers="" type="drawable"/><file name="ic_action_home" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-anydpi\ic_action_home.xml" qualifiers="anydpi-v4" type="drawable"/><file name="ic_action_home" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-hdpi\ic_action_home.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_action_ok" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-hdpi\ic_action_ok.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_action_home" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-mdpi\ic_action_home.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_action_ok" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-mdpi\ic_action_ok.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="ic_action_home" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-xhdpi\ic_action_home.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_action_ok" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-xhdpi\ic_action_ok.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_action_home" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-xxhdpi\ic_action_home.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_action_ok" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-xxhdpi\ic_action_ok.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_action_ok" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-xxxhdpi\ic_action_ok.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="activity_float_item" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_float_item.xml" qualifiers="" type="layout"/><file name="activity_login" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_main" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="fragment_autojs" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_autojs.xml" qualifiers="" type="layout"/><file name="fragment_debug" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml" qualifiers="" type="layout"/><file name="fragment_main" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml" qualifiers="" type="layout"/><file name="fragment_settings" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_settings.xml" qualifiers="" type="layout"/><file name="fragment_simple_autojs" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_simple_autojs.xml" qualifiers="" type="layout"/><file name="phone_row" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\phone_row.xml" qualifiers="" type="layout"/><file name="setting_row" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\setting_row.xml" qualifiers="" type="layout"/><file name="sms_row" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="sliant" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\raw\sliant.mp3" qualifiers="" type="raw"/><file name="test_script" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\raw\test_script.js" qualifiers="" type="raw"/><file name="toutiao_browsing_script" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\raw\toutiao_browsing_script.js" qualifiers="" type="raw"/><file path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\arrays.xml" qualifiers=""><string-array name="reply_entries">
        <item>Reply</item>
        <item>Reply to all</item>
    </string-array><string-array name="reply_values">
        <item>reply</item>
        <item>reply_all</item>
    </string-array></file><file path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="green">#4CAF50</color><color name="app_background_color">#ECF5EE</color><color name="text_primary">#37474F</color><color name="text_secondary">#607D8B</color><color name="header_background">#DAEADC</color><color name="surface_background">#F8FFF9</color><color name="accent_color">#81C784</color><color name="check_mark_color">#FFECB3</color><color name="button_primary">#66BB6A</color><color name="colorPrimary">#2196F3</color><color name="colorPrimaryDark">#1976D2</color><color name="colorAccent">#FF5722</color><color name="colorSecondary">#4CAF50</color></file><file path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="fab_margin">16dp</dimen></file><file path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">android-tool</string><string name="action_settings">Settings</string><string name="first_fragment_label">First Fragment</string><string name="second_fragment_label">Second Fragment</string><string name="next">Next</string><string name="previous">Previous</string><string name="lorem_ipsum">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam in scelerisque sem. Mauris
        volutpat, dolor id interdum ullamcorper, risus dolor egestas lectus, sit amet mattis purus
        dui nec risus. Maecenas non sodales nisi, vel dictum dolor. Class aptent taciti sociosqu ad
        litora torquent per conubia nostra, per inceptos himenaeos. Suspendisse blandit eleifend
        diam, vel rutrum tellus vulputate quis. Aliquam eget libero aliquet, imperdiet nisl a,
        ornare ex. Sed rhoncus est ut libero porta lobortis. Fusce in dictum tellus.\n\n
        Suspendisse interdum ornare ante. Aliquam nec cursus lorem. Morbi id magna felis. Vivamus
        egestas, est a condimentum egestas, turpis nisl iaculis ipsum, in dictum tellus dolor sed
        neque. Morbi tellus erat, dapibus ut sem a, iaculis tincidunt dui. Interdum et malesuada
        fames ac ante ipsum primis in faucibus. Curabitur et eros porttitor, ultricies urna vitae,
        molestie nibh. Phasellus at commodo eros, non aliquet metus. Sed maximus nisl nec dolor
        bibendum, vel congue leo egestas.\n\n
        Sed interdum tortor nibh, in sagittis risus mollis quis. Curabitur mi odio, condimentum sit
        amet auctor at, mollis non turpis. Nullam pretium libero vestibulum, finibus orci vel,
        molestie quam. Fusce blandit tincidunt nulla, quis sollicitudin libero facilisis et. Integer
        interdum nunc ligula, et fermentum metus hendrerit id. Vestibulum lectus felis, dictum at
        lacinia sit amet, tristique id quam. Cras eu consequat dui. Suspendisse sodales nunc ligula,
        in lobortis sem porta sed. Integer id ultrices magna, in luctus elit. Sed a pellentesque
        est.\n\n
        Aenean nunc velit, lacinia sed dolor sed, ultrices viverra nulla. Etiam a venenatis nibh.
        Morbi laoreet, tortor sed facilisis varius, nibh orci rhoncus nulla, id elementum leo dui
        non lorem. Nam mollis ipsum quis auctor varius. Quisque elementum eu libero sed commodo. In
        eros nisl, imperdiet vel imperdiet et, scelerisque a mauris. Pellentesque varius ex nunc,
        quis imperdiet eros placerat ac. Duis finibus orci et est auctor tincidunt. Sed non viverra
        ipsum. Nunc quis augue egestas, cursus lorem at, molestie sem. Morbi a consectetur ipsum, a
        placerat diam. Etiam vulputate dignissim convallis. Integer faucibus mauris sit amet finibus
        convallis.\n\n
        Phasellus in aliquet mi. Pellentesque habitant morbi tristique senectus et netus et
        malesuada fames ac turpis egestas. In volutpat arcu ut felis sagittis, in finibus massa
        gravida. Pellentesque id tellus orci. Integer dictum, lorem sed efficitur ullamcorper,
        libero justo consectetur ipsum, in mollis nisl ex sed nisl. Donec maximus ullamcorper
        sodales. Praesent bibendum rhoncus tellus nec feugiat. In a ornare nulla. Donec rhoncus
        libero vel nunc consequat, quis tincidunt nisl eleifend. Cras bibendum enim a justo luctus
        vestibulum. Fusce dictum libero quis erat maximus, vitae volutpat diam dignissim.
    </string><string name="title_activity_settings">SettingsActivity</string><string name="messages_header">Messages</string><string name="sync_header">Sync</string><string name="signature_title">Your signature</string><string name="reply_title">Default reply action</string><string name="sync_title">Sync email periodically</string><string name="attachment_title">Download incoming attachments</string><string name="attachment_summary_on">Automatically download attachments for incoming emails
    </string><string name="attachment_summary_off">Only download attachments when manually requested</string><string name="autojs_accessibility_service_description">AutoJs6自动化脚本执行服务，用于实现手机自动化操作。启用此服务后，AutoJs6可以模拟点击、滑动、输入等操作来执行自动化脚本。</string></file><file path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.AndroidTool" parent="Theme.Material3.DayNight.NoActionBar">
        
         <item name="colorPrimary">#F5F5F5</item>
        <item name="android:statusBarColor" ns1:targetApi="l">#000000</item> 
        <item name="colorControlNormal">#000000</item>
        <item name="colorControlActivated">@color/green</item>
        <item name="colorControlHighlight">@color/green</item>
    </style><style name="Theme.AndroidTool" parent="Base.Theme.AndroidTool"/></file><file path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values-land\dimens.xml" qualifiers="land"><dimen name="fab_margin">48dp</dimen></file><file path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.Snettool" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">#F5F5F5</item>
        <item name="android:statusBarColor" ns1:targetApi="l">#000000</item> 
    </style></file><file path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values-v23\themes.xml" qualifiers="v23"><style name="Theme.Snettool" parent="Base.Theme.Snettool">
        
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">?attr/isLightTheme</item>
    </style></file><file path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values-w1240dp\dimens.xml" qualifiers="w1240dp-v13"><dimen name="fab_margin">200dp</dimen></file><file path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values-w600dp\dimens.xml" qualifiers="w600dp-v13"><dimen name="fab_margin">48dp</dimen></file><file name="allocation" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\xml\allocation.xml" qualifiers="" type="xml"/><file name="autojs_accessibility_service" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\xml\autojs_accessibility_service.xml" qualifiers="" type="xml"/><file name="backup_rules" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>
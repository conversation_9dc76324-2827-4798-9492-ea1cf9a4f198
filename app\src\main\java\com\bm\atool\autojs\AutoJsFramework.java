package com.bm.atool.autojs;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.util.Log;

import com.bm.atool.autojs.engine.ExecutionEngine;
import com.bm.atool.autojs.manager.ScriptManager;
import com.bm.atool.autojs.model.ExecutionContext;
import com.bm.atool.autojs.model.Script;
import com.bm.atool.autojs.security.SecurityManager;
import com.bm.atool.autojs.api.APIBridge;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.lang.reflect.Method;

/**
 * AutoJS 6 框架核心类
 * 统一管理脚本执行、API桥接、安全控制等功能
 */
public class AutoJsFramework {
    private static final String TAG = "AutoJsFramework";
    
    private static volatile AutoJsFramework instance;
    private Context context;
    private boolean isInitialized = false;
    
    // 核心组件
    private ScriptManager scriptManager;
    private ExecutionEngine executionEngine;
    private APIBridge apiBridge;
    private SecurityManager securityManager;
    
    // 执行状态管理
    private ConcurrentHashMap<String, ExecutionContext> runningScripts;
    private List<ScriptExecutionCallback> globalCallbacks;
    
    private AutoJsFramework() {
        runningScripts = new ConcurrentHashMap<>();
        globalCallbacks = new CopyOnWriteArrayList<>();
    }
    
    /**
     * 获取单例实例
     */
    public static AutoJsFramework getInstance() {
        if (instance == null) {
            synchronized (AutoJsFramework.class) {
                if (instance == null) {
                    instance = new AutoJsFramework();
                }
            }
        }
        return instance;
    }
    
    /**
     * 获取进程名称
     */
    private static String getProcessName(Application application) {
        try {
            if (Build.VERSION.SDK_INT >= 28) {
                return Application.getProcessName();
            }
            
            // 使用反射获取进程名称
            Class<?> activityThread = Class.forName("android.app.ActivityThread");
            String methodName = Build.VERSION.SDK_INT >= 18 ? "currentProcessName" : "currentPackageName";
            Method getProcessName = activityThread.getDeclaredMethod(methodName);
            return (String) getProcessName.invoke(null);
        } catch (Exception e) {
            Log.e(TAG, "Error getting process name", e);
            return "unknown";
        }
    }
    
    /**
     * 检查进程是否正在重新创建
     */
    private static boolean isProcessRecreating(Application application) {
        try {
            String processName = getProcessName(application);
            SharedPreferences prefs = application.getSharedPreferences("AutoJsFramework", Context.MODE_PRIVATE);
            
            // 获取进程特定的最后初始化时间
            String processKey = "last_init_time_" + processName;
            long lastInitTime = prefs.getLong(processKey, 0);
            long currentTime = System.currentTimeMillis();
            
            // 获取全局最后初始化时间（所有进程）
            long globalLastInitTime = prefs.getLong("global_last_init_time", 0);
            
            // 如果上次初始化时间在3秒内，说明进程正在重新创建
            if (lastInitTime > 0 && (currentTime - lastInitTime) < 3000) {
                Log.w(TAG, "Process recreating detected for " + processName + " - last init: " + lastInitTime + ", current: " + currentTime);
                return true;
            }
            
            // 如果全局初始化时间在5秒内，且当前进程不是主进程，则跳过初始化
            if (globalLastInitTime > 0 && (currentTime - globalLastInitTime) < 5000 && !processName.equals(application.getPackageName())) {
                Log.w(TAG, "Skipping initialization for subprocess " + processName + " - global init: " + globalLastInitTime + ", current: " + currentTime);
                return true;
            }
            
            // 记录本次初始化时间
            prefs.edit()
                .putLong(processKey, currentTime)
                .putLong("global_last_init_time", currentTime)
                .apply();
            
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking process recreation", e);
            return false;
        }
    }
    
    /**
     * 初始化框架
     */
    public synchronized boolean initialize(Application application) {
        // 检查是否已经初始化
        if (isInitialized) {
            Log.d(TAG, "AutoJsFramework already initialized");
            return true;
        }
        
        // 检查进程状态，避免在进程重启时重复初始化
        String processName = getProcessName(application);
        if (isProcessRecreating(application)) {
            Log.w(TAG, "Process is recreating, skipping AutoJsFramework initialization: " + processName);
            return false;
        }
        
        try {
            Log.d(TAG, "Initializing AutoJsFramework for process: " + processName);
            this.context = application.getApplicationContext();
            
            // 初始化核心组件
            initializeComponents();
            
            isInitialized = true;
            Log.d(TAG, "AutoJsFramework initialized successfully for process: " + processName);
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize AutoJsFramework for process: " + processName, e);
            return false;
        }
    }
    
    /**
     * 初始化核心组件
     */
    private void initializeComponents() {
        Log.d(TAG, "Initializing core components...");
        
        // 初始化安全管理器
        securityManager = new SecurityManager(context);
        
        // 初始化API桥接
        apiBridge = new APIBridge(context, securityManager);
        
        // 初始化脚本管理器
        scriptManager = new ScriptManager(context, securityManager);
        
        // 初始化执行引擎
        executionEngine = new ExecutionEngine(context, apiBridge, securityManager);
        
        Log.d(TAG, "Core components initialized successfully");
    }
    
    /**
     * 检查是否已初始化
     */
    public boolean isInitialized() {
        return isInitialized;
    }
    
    /**
     * 执行脚本
     */
    public String executeScript(String scriptContent, String scriptName, ScriptExecutionCallback callback) {
        if (!isInitialized) {
            Log.e(TAG, "AutoJsFramework not initialized");
            if (callback != null) {
                callback.onError(scriptName, "框架未初始化");
            }
            return null;
        }
        
        try {
            // 创建脚本对象
            Script script = new Script(scriptName, scriptContent);
            
            // 验证脚本
            if (!scriptManager.validateScript(script)) {
                String error = "脚本验证失败";
                Log.e(TAG, error + ": " + scriptName);
                if (callback != null) {
                    callback.onError(scriptName, error);
                }
                return null;
            }
            
            // 检查权限
            if (!securityManager.checkPermissions(script)) {
                String error = "权限检查失败";
                Log.e(TAG, error + ": " + scriptName);
                if (callback != null) {
                    callback.onError(scriptName, error);
                }
                return null;
            }
            
            // 执行脚本
            ExecutionContext context = executionEngine.executeScript(script, new ExecutionEngine.ExecutionCallback() {
                @Override
                public void onStart(ExecutionContext context) {
                    runningScripts.put(context.getExecutionId(), context);
                    Log.d(TAG, "Script started: " + scriptName);
                    
                    if (callback != null) {
                        callback.onStart(scriptName);
                    }
                    notifyGlobalCallbacks(cb -> cb.onStart(scriptName));
                }
                
                @Override
                public void onSuccess(ExecutionContext context, String result) {
                    runningScripts.remove(context.getExecutionId());
                    Log.d(TAG, "Script completed: " + scriptName + ", result: " + result);
                    
                    if (callback != null) {
                        callback.onSuccess(scriptName, result);
                    }
                    notifyGlobalCallbacks(cb -> cb.onSuccess(scriptName, result));
                }
                
                @Override
                public void onError(ExecutionContext context, String error) {
                    runningScripts.remove(context.getExecutionId());
                    Log.e(TAG, "Script failed: " + scriptName + ", error: " + error);
                    
                    if (callback != null) {
                        callback.onError(scriptName, error);
                    }
                    notifyGlobalCallbacks(cb -> cb.onError(scriptName, error));
                }
            });
            
            return context != null ? context.getExecutionId() : null;
            
        } catch (Exception e) {
            Log.e(TAG, "Error executing script: " + scriptName, e);
            if (callback != null) {
                callback.onError(scriptName, e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * 停止指定脚本
     */
    public boolean stopScript(String executionId) {
        try {
            ExecutionContext context = runningScripts.get(executionId);
            if (context != null) {
                boolean success = executionEngine.stopExecution(executionId);
                if (success) {
                    runningScripts.remove(executionId);
                    Log.d(TAG, "Script stopped: " + executionId);
                }
                return success;
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error stopping script: " + executionId, e);
            return false;
        }
    }
    
    /**
     * 停止所有脚本
     */
    public int stopAllScripts() {
        try {
            int count = 0;
            for (String executionId : runningScripts.keySet()) {
                if (stopScript(executionId)) {
                    count++;
                }
            }
            Log.d(TAG, "Stopped " + count + " scripts");
            return count;
        } catch (Exception e) {
            Log.e(TAG, "Error stopping all scripts", e);
            return 0;
        }
    }
    
    /**
     * 获取运行中的脚本数量
     */
    public int getRunningScriptCount() {
        return runningScripts.size();
    }
    
    /**
     * 获取运行中的脚本列表
     */
    public List<ExecutionContext> getRunningScripts() {
        return new CopyOnWriteArrayList<>(runningScripts.values());
    }
    
    /**
     * 检查无障碍服务是否启用
     */
    public boolean isAccessibilityServiceEnabled() {
        return securityManager.isAccessibilityServiceEnabled();
    }
    
    /**
     * 添加全局回调
     */
    public void addGlobalCallback(ScriptExecutionCallback callback) {
        if (callback != null && !globalCallbacks.contains(callback)) {
            globalCallbacks.add(callback);
        }
    }
    
    /**
     * 移除全局回调
     */
    public void removeGlobalCallback(ScriptExecutionCallback callback) {
        globalCallbacks.remove(callback);
    }
    
    /**
     * 通知全局回调
     */
    private void notifyGlobalCallbacks(CallbackNotifier notifier) {
        for (ScriptExecutionCallback callback : globalCallbacks) {
            try {
                notifier.notify(callback);
            } catch (Exception e) {
                Log.e(TAG, "Error notifying global callback", e);
            }
        }
    }
    
    /**
     * 关闭框架
     */
    public synchronized void shutdown() {
        if (!isInitialized) {
            return;
        }
        
        try {
            Log.d(TAG, "Shutting down AutoJsFramework...");
            
            // 停止所有脚本
            stopAllScripts();
            
            // 清理组件
            if (executionEngine != null) {
                executionEngine.shutdown();
            }
            if (scriptManager != null) {
                scriptManager.shutdown();
            }
            if (apiBridge != null) {
                apiBridge.shutdown();
            }
            if (securityManager != null) {
                securityManager.shutdown();
            }
            
            // 清理状态
            runningScripts.clear();
            globalCallbacks.clear();
            
            isInitialized = false;
            Log.d(TAG, "AutoJsFramework shutdown completed");
            
        } catch (Exception e) {
            Log.e(TAG, "Error during shutdown", e);
        }
    }
    
    // Getters for components
    public ScriptManager getScriptManager() {
        return scriptManager;
    }
    
    public ExecutionEngine getExecutionEngine() {
        return executionEngine;
    }
    
    public APIBridge getApiBridge() {
        return apiBridge;
    }
    
    public SecurityManager getSecurityManager() {
        return securityManager;
    }
    
    /**
     * 脚本执行回调接口
     */
    public interface ScriptExecutionCallback {
        void onStart(String scriptName);
        void onSuccess(String scriptName, String result);
        void onError(String scriptName, String error);
    }
    
    /**
     * 回调通知器接口
     */
    private interface CallbackNotifier {
        void notify(ScriptExecutionCallback callback);
    }
}

<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2022 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  android:width="@dimen/mtrl_switch_track_width"
  android:height="@dimen/mtrl_switch_track_height"
  android:viewportWidth="@integer/mtrl_switch_track_viewport_width"
  android:viewportHeight="@integer/mtrl_switch_track_viewport_height"
  tools:ignore="NewApi">

  <path
    android:name="track"
    android:fillColor="#FFFFFFFF"
    android:pathData="@string/mtrl_switch_track_path" />

</vector>

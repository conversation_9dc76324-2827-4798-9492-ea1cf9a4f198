<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_simple_autojs" modulePackage="com.bm.atool" filePath="app\src\main\res\layout\fragment_simple_autojs.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_simple_autojs_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="352" endOffset="12"/></Target><Target id="@+id/tvScriptStatus" view="TextView"><Expressions/><location startLine="32" startOffset="12" endLine="39" endOffset="51"/></Target><Target id="@+id/tvRunningScripts" view="TextView"><Expressions/><location startLine="41" startOffset="12" endLine="47" endOffset="58"/></Target><Target id="@+id/etScriptName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="60" startOffset="12" endLine="66" endOffset="43"/></Target><Target id="@+id/etScriptContent" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="79" startOffset="12" endLine="89" endOffset="68"/></Target><Target id="@+id/btnTestBasic" view="Button"><Expressions/><location startLine="111" startOffset="12" endLine="120" endOffset="39"/></Target><Target id="@+id/btnTestIntermediate" view="Button"><Expressions/><location startLine="122" startOffset="12" endLine="132" endOffset="39"/></Target><Target id="@+id/btnTestAdvanced" view="Button"><Expressions/><location startLine="134" startOffset="12" endLine="143" endOffset="39"/></Target><Target id="@+id/btnTestConsoleAPI" view="Button"><Expressions/><location startLine="154" startOffset="12" endLine="163" endOffset="39"/></Target><Target id="@+id/btnTestUtilsAPI" view="Button"><Expressions/><location startLine="165" startOffset="12" endLine="175" endOffset="39"/></Target><Target id="@+id/btnTestSystemAPI" view="Button"><Expressions/><location startLine="177" startOffset="12" endLine="186" endOffset="39"/></Target><Target id="@+id/btnTestAllAPIs" view="Button"><Expressions/><location startLine="197" startOffset="12" endLine="206" endOffset="39"/></Target><Target id="@+id/btnTestPerformance" view="Button"><Expressions/><location startLine="208" startOffset="12" endLine="218" endOffset="39"/></Target><Target id="@+id/btnTestStress" view="Button"><Expressions/><location startLine="220" startOffset="12" endLine="229" endOffset="39"/></Target><Target id="@+id/btnToutiaoScript" view="Button"><Expressions/><location startLine="250" startOffset="12" endLine="259" endOffset="40"/></Target><Target id="@+id/btnTestAutoJS" view="Button"><Expressions/><location startLine="261" startOffset="12" endLine="270" endOffset="40"/></Target><Target id="@+id/btnExecuteScript" view="Button"><Expressions/><location startLine="290" startOffset="12" endLine="298" endOffset="40"/></Target><Target id="@+id/btnStopAllScripts" view="Button"><Expressions/><location startLine="305" startOffset="16" endLine="314" endOffset="44"/></Target><Target id="@+id/btnCheckStatus" view="Button"><Expressions/><location startLine="316" startOffset="16" endLine="324" endOffset="44"/></Target></Targets></Layout>
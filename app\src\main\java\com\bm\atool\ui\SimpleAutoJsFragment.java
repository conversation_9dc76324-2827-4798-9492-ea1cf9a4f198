package com.bm.atool.ui;

import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.bm.atool.R;
import com.bm.autojs6.module.AutoJs6Module;
import com.bm.autojs6.module.model.Script;
import com.bm.autojs6.module.model.ScriptResult;

import java.io.InputStream;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.IOException;

/**
 * 简化的AutoJs脚本执行Fragment
 * 提供基本的JS脚本编辑和执行功能
 */
public class SimpleAutoJsFragment extends BaseFragment {
    private static final String TAG = "SimpleAutoJsFragment";
    
    private EditText etScriptContent;
    private EditText etScriptName;
    private Button btnExecuteScript;
    private Button btnStopAllScripts;
    private Button btnCheckStatus;
    private TextView tvScriptStatus;
    private TextView tvRunningScripts;

    // 测试按钮
    private Button btnTestBasic;
    private Button btnTestIntermediate;
    private Button btnTestAdvanced;
    private Button btnTestConsoleAPI;
    private Button btnTestUtilsAPI;
    private Button btnTestSystemAPI;
    private Button btnTestAllAPIs;
    private Button btnTestPerformance;
    private Button btnTestStress;

    // 实用脚本按钮
    private Button btnToutiaoScript;
    private Button btnTestAutoJS;
    
    private AutoJs6Module autoJs6Module;
    private Handler uiHandler;
    
    public SimpleAutoJsFragment() {
        super();
        this.setTitle("JS脚本执行器");
    }
    
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_simple_autojs, container, false);
        
        initViews(view);
        initAutoJs();
        setupClickListeners();
        updateUI();
        
        return view;
    }
    
    private void initViews(View view) {
        etScriptContent = view.findViewById(R.id.etScriptContent);
        etScriptName = view.findViewById(R.id.etScriptName);
        btnExecuteScript = view.findViewById(R.id.btnExecuteScript);
        btnStopAllScripts = view.findViewById(R.id.btnStopAllScripts);
        btnCheckStatus = view.findViewById(R.id.btnCheckStatus);
        tvScriptStatus = view.findViewById(R.id.tvScriptStatus);
        tvRunningScripts = view.findViewById(R.id.tvRunningScripts);

        // 初始化测试按钮
        btnTestBasic = view.findViewById(R.id.btnTestBasic);
        btnTestIntermediate = view.findViewById(R.id.btnTestIntermediate);
        btnTestAdvanced = view.findViewById(R.id.btnTestAdvanced);
        btnTestConsoleAPI = view.findViewById(R.id.btnTestConsoleAPI);
        btnTestUtilsAPI = view.findViewById(R.id.btnTestUtilsAPI);
        btnTestSystemAPI = view.findViewById(R.id.btnTestSystemAPI);
        btnTestAllAPIs = view.findViewById(R.id.btnTestAllAPIs);
        btnTestPerformance = view.findViewById(R.id.btnTestPerformance);
        btnTestStress = view.findViewById(R.id.btnTestStress);

        // 初始化实用脚本按钮
        btnToutiaoScript = view.findViewById(R.id.btnToutiaoScript);
        btnTestAutoJS = view.findViewById(R.id.btnTestAutoJS);

        uiHandler = new Handler();

        // 设置默认脚本内容
        setDefaultScript();
    }
    
    private void initAutoJs() {
        autoJs6Module = AutoJs6Module.getInstance();
        if (!autoJs6Module.isInitialized()) {
            autoJs6Module.initialize(getContext());
            Log.d(TAG, "AutoJs6Module initialized");
        } else {
            Log.d(TAG, "AutoJs6Module already initialized");
        }
    }
    
    private void setupClickListeners() {
        btnExecuteScript.setOnClickListener(v -> executeScript());
        btnStopAllScripts.setOnClickListener(v -> stopAllScripts());
        btnCheckStatus.setOnClickListener(v -> checkStatus());

        // 测试按钮点击事件
        btnTestBasic.setOnClickListener(v -> runBasicTest());
        btnTestIntermediate.setOnClickListener(v -> runIntermediateTest());
        btnTestAdvanced.setOnClickListener(v -> runAdvancedTest());
        btnTestConsoleAPI.setOnClickListener(v -> runConsoleAPITest());
        btnTestUtilsAPI.setOnClickListener(v -> runUtilsAPITest());
        btnTestSystemAPI.setOnClickListener(v -> runSystemAPITest());
        btnTestAllAPIs.setOnClickListener(v -> runAllAPIsTest());
        btnTestPerformance.setOnClickListener(v -> runPerformanceTest());
        btnTestStress.setOnClickListener(v -> runStressTest());

        // 实用脚本按钮点击事件
        btnToutiaoScript.setOnClickListener(v -> runToutiaoScript());
        btnTestAutoJS.setOnClickListener(v -> runAutoJSTest());
    }
    
    private void setDefaultScript() {
        String defaultScript = "// AutoJs6 Module 测试脚本\n" +
                "console.log('Hello AutoJs6 Module!');\n\n" +
                "// 计算示例\n" +
                "var a = 10;\n" +
                "var b = 20;\n" +
                "var result = a + b;\n" +
                "console.log('计算结果: ' + a + ' + ' + b + ' = ' + result);\n\n" +
                "// 循环示例\n" +
                "for (var i = 1; i <= 3; i++) {\n" +
                "    console.log('循环第 ' + i + ' 次');\n" +
                "}\n\n" +
                "// 函数定义和调用\n" +
                "function greet(name) {\n" +
                "    return 'Hello, ' + name + '!';\n" +
                "}\n\n" +
                "var greeting = greet('AutoJs6');\n" +
                "console.log(greeting);\n\n" +
                "// 对象操作\n" +
                "var person = {\n" +
                "    name: '张三',\n" +
                "    age: 25\n" +
                "};\n" +
                "console.log('姓名: ' + person.name + ', 年龄: ' + person.age);\n\n" +
                "// 测试utils工具函数\n" +
                "if (typeof utils !== 'undefined') {\n" +
                "    utils.log('这是通过utils.log输出的消息');\n" +
                "    utils.sleep(100);\n" +
                "    utils.toast('测试提示消息');\n" +
                "} else {\n" +
                "    console.log('utils对象未定义');\n" +
                "}\n\n" +
                "// 测试Android信息\n" +
                "if (typeof android !== 'undefined') {\n" +
                "    console.log('包名: ' + android.context.getPackageName());\n" +
                "    console.log('设备信息: ' + android.device.brand + ' ' + android.device.model);\n" +
                "    console.log('屏幕尺寸: ' + android.device.width + 'x' + android.device.height);\n" +
                "} else {\n" +
                "    console.log('android对象未定义');\n" +
                "}\n\n" +
                "console.log('脚本执行完成!');\n" +
                "'脚本执行成功 - 所有功能正常';";

        etScriptContent.setText(defaultScript);
        etScriptName.setText("AutoJs6测试脚本");
    }
    
    private void executeScript() {
        String scriptContent = etScriptContent.getText().toString().trim();
        String scriptNameTemp = etScriptName.getText().toString().trim();

        Log.d(TAG, "=== 开始执行脚本 ===");
        Log.d(TAG, "脚本名称: " + scriptNameTemp);
        Log.d(TAG, "脚本长度: " + scriptContent.length() + " 字符");

        if (TextUtils.isEmpty(scriptContent)) {
            Log.w(TAG, "脚本内容为空");
            showToast("请输入脚本内容");
            return;
        }

        final String scriptName = TextUtils.isEmpty(scriptNameTemp) ? "未命名脚本" : scriptNameTemp;

        if (!autoJs6Module.isInitialized()) {
            Log.e(TAG, "AutoJs6Module未初始化");
            showToast("JS引擎未初始化");
            return;
        }

        Log.d(TAG, "AutoJs6Module状态: 已初始化");
        Log.d(TAG, "当前运行脚本数: " + autoJs6Module.getRunningScriptCount());

        try {
            // 创建脚本对象
            Log.d(TAG, "创建脚本对象...");
            Script script = new Script(scriptName, scriptContent);
            Log.d(TAG, "脚本对象创建成功");

            // 记录脚本开始执行时间
            final long startTime = System.currentTimeMillis();
            Log.d(TAG, "脚本开始执行时间: " + new java.util.Date(startTime));

            // 异步执行脚本
            Log.d(TAG, "提交脚本异步执行...");
            autoJs6Module.executeScriptAsync(script)
                .thenAccept(result -> {
                    long endTime = System.currentTimeMillis();
                    long duration = endTime - startTime;

                    Log.d(TAG, "=== 脚本执行完成 ===");
                    Log.d(TAG, "执行时长: " + duration + "ms");
                    Log.d(TAG, "执行结果: " + (result.isSuccess() ? "成功" : "失败"));

                    uiHandler.post(() -> {
                        if (result.isSuccess()) {
                            Log.i(TAG, "脚本执行成功: " + scriptName);
                            Log.d(TAG, "执行结果内容: " + result.getResult());
                            tvScriptStatus.setText("脚本执行成功: " + scriptName + " (耗时: " + duration + "ms)");
                            showToast("脚本执行成功 (耗时: " + duration + "ms)");
                        } else {
                            Log.e(TAG, "脚本执行失败: " + scriptName);
                            Log.e(TAG, "错误信息: " + result.getError());
                            tvScriptStatus.setText("脚本执行失败: " + scriptName);
                            showToast("脚本执行失败: " + result.getError());
                        }
                        updateRunningScriptsCount();
                        Log.d(TAG, "UI更新完成");
                    });
                })
                .exceptionally(throwable -> {
                    long endTime = System.currentTimeMillis();
                    long duration = endTime - startTime;

                    Log.e(TAG, "=== 脚本执行异常 ===");
                    Log.e(TAG, "执行时长: " + duration + "ms");
                    Log.e(TAG, "异常信息: " + throwable.getMessage(), throwable);

                    uiHandler.post(() -> {
                        tvScriptStatus.setText("脚本执行异常: " + scriptName + " (耗时: " + duration + "ms)");
                        showToast("脚本执行异常: " + throwable.getMessage());
                        updateRunningScriptsCount();
                        Log.d(TAG, "异常UI更新完成");
                    });
                    return null;
                });

            Log.d(TAG, "脚本已提交执行");
            showToast("脚本开始执行: " + scriptName);
            tvScriptStatus.setText("脚本正在执行: " + scriptName);
            updateRunningScriptsCount();

        } catch (Exception e) {
            Log.e(TAG, "执行脚本时发生异常", e);
            Log.e(TAG, "异常类型: " + e.getClass().getSimpleName());
            Log.e(TAG, "异常消息: " + e.getMessage());
            showToast("脚本执行出错: " + e.getMessage());
            tvScriptStatus.setText("脚本执行出错: " + e.getMessage());
        }

        Log.d(TAG, "executeScript方法执行完成");
    }
    
    private void stopAllScripts() {
        Log.d(TAG, "=== 开始停止所有脚本 ===");
        try {
            int beforeCount = autoJs6Module.getRunningScriptCount();
            Log.d(TAG, "停止前运行脚本数: " + beforeCount);

            autoJs6Module.stopAllScripts();

            int afterCount = autoJs6Module.getRunningScriptCount();
            Log.d(TAG, "停止后运行脚本数: " + afterCount);
            Log.i(TAG, "成功停止 " + (beforeCount - afterCount) + " 个脚本");

            showToast("已停止所有脚本");
            tvScriptStatus.setText("已停止所有脚本");
            updateRunningScriptsCount();
        } catch (Exception e) {
            Log.e(TAG, "停止脚本时发生异常", e);
            Log.e(TAG, "异常类型: " + e.getClass().getSimpleName());
            Log.e(TAG, "异常消息: " + e.getMessage());
            showToast("停止脚本失败: " + e.getMessage());
        }
        Log.d(TAG, "stopAllScripts方法执行完成");
    }

    private void checkStatus() {
        Log.d(TAG, "=== 开始检查状态 ===");

        if (!autoJs6Module.isInitialized()) {
            Log.w(TAG, "AutoJs6Module未初始化");
            showToast("JS引擎未初始化");
            return;
        }

        try {
            int runningCount = autoJs6Module.getRunningScriptCount();
            Log.d(TAG, "当前运行脚本数: " + runningCount);

            // 获取更多状态信息
            boolean isInitialized = autoJs6Module.isInitialized();
            Log.d(TAG, "引擎初始化状态: " + isInitialized);

            String status = String.format("JS引擎状态: 正常\n运行中的脚本: %d\n初始化状态: %s",
                runningCount, isInitialized ? "已初始化" : "未初始化");

            Log.i(TAG, "状态检查完成 - 引擎正常运行");
            showToast("JS引擎运行正常 (运行脚本: " + runningCount + ")");
            tvScriptStatus.setText(status);
            updateRunningScriptsCount();
        } catch (Exception e) {
            Log.e(TAG, "检查状态时发生异常", e);
            Log.e(TAG, "异常类型: " + e.getClass().getSimpleName());
            Log.e(TAG, "异常消息: " + e.getMessage());
            showToast("状态检查失败: " + e.getMessage());
            tvScriptStatus.setText("状态检查失败: " + e.getMessage());
        }

        Log.d(TAG, "checkStatus方法执行完成");
    }

    private void updateRunningScriptsCount() {
        try {
            int count = autoJs6Module.getRunningScriptCount();
            Log.v(TAG, "更新运行脚本计数: " + count);
            tvRunningScripts.setText("运行中的脚本: " + count);
        } catch (Exception e) {
            Log.e(TAG, "获取运行脚本数量时发生异常", e);
            Log.e(TAG, "异常类型: " + e.getClass().getSimpleName());
            Log.e(TAG, "异常消息: " + e.getMessage());
            tvRunningScripts.setText("运行中的脚本: 未知");
        }
    }
    
    private void updateUI() {
        updateRunningScriptsCount();
        
        if (autoJs6Module.isInitialized()) {
            tvScriptStatus.setText("JS引擎已就绪");
        } else {
            tvScriptStatus.setText("JS引擎未初始化");
        }
    }
    
    // ========== 测试方法 ==========

    private void runBasicTest() {
        Log.d(TAG, "开始基础测试");
        String script = "// 基础测试脚本\n" +
                "console.log('=== 基础测试开始 ===');\n" +
                "console.log('测试时间: ' + new Date().toString());\n\n" +

                "// 1. 基本数据类型测试\n" +
                "console.log('1. 数据类型测试:');\n" +
                "var num = 42;\n" +
                "var str = 'Hello World';\n" +
                "var bool = true;\n" +
                "var arr = [1, 2, 3];\n" +
                "var obj = {name: 'test', value: 100};\n" +
                "console.log('数字: ' + num + ', 类型: ' + typeof num);\n" +
                "console.log('字符串: ' + str + ', 类型: ' + typeof str);\n" +
                "console.log('布尔: ' + bool + ', 类型: ' + typeof bool);\n" +
                "console.log('数组: [' + arr.join(', ') + '], 长度: ' + arr.length);\n" +
                "console.log('对象: ' + JSON.stringify(obj));\n\n" +

                "// 2. 基本运算测试\n" +
                "console.log('2. 运算测试:');\n" +
                "var a = 10, b = 3;\n" +
                "console.log(a + ' + ' + b + ' = ' + (a + b));\n" +
                "console.log(a + ' - ' + b + ' = ' + (a - b));\n" +
                "console.log(a + ' * ' + b + ' = ' + (a * b));\n" +
                "console.log(a + ' / ' + b + ' = ' + (a / b));\n" +
                "console.log(a + ' % ' + b + ' = ' + (a % b));\n\n" +

                "// 3. 循环测试\n" +
                "console.log('3. 循环测试:');\n" +
                "for (var i = 1; i <= 3; i++) {\n" +
                "    console.log('for循环第 ' + i + ' 次');\n" +
                "}\n" +
                "var j = 1;\n" +
                "while (j <= 3) {\n" +
                "    console.log('while循环第 ' + j + ' 次');\n" +
                "    j++;\n" +
                "}\n\n" +

                "console.log('=== 基础测试完成 ===');";

        executeTestScript("基础测试", script);
    }

    private void runIntermediateTest() {
        Log.d(TAG, "开始中级测试");
        String script = "// 中级测试脚本\n" +
                "console.log('=== 中级测试开始 ===');\n" +
                "console.log('测试时间: ' + new Date().toString());\n\n" +

                "// 1. 函数定义和调用测试\n" +
                "console.log('1. 函数测试:');\n" +
                "function add(x, y) {\n" +
                "    return x + y;\n" +
                "}\n" +
                "function factorial(n) {\n" +
                "    if (n <= 1) return 1;\n" +
                "    return n * factorial(n - 1);\n" +
                "}\n" +
                "console.log('add(5, 3) = ' + add(5, 3));\n" +
                "console.log('factorial(5) = ' + factorial(5));\n\n" +

                "// 2. 数组操作测试\n" +
                "console.log('2. 数组操作测试:');\n" +
                "var numbers = [1, 2, 3, 4, 5];\n" +
                "console.log('原数组: [' + numbers.join(', ') + ']');\n" +
                "numbers.push(6);\n" +
                "console.log('push(6)后: [' + numbers.join(', ') + ']');\n" +
                "var popped = numbers.pop();\n" +
                "console.log('pop()返回: ' + popped + ', 数组: [' + numbers.join(', ') + ']');\n" +
                "var doubled = numbers.map(function(x) { return x * 2; });\n" +
                "console.log('map(*2): [' + doubled.join(', ') + ']');\n" +
                "var filtered = numbers.filter(function(x) { return x > 2; });\n" +
                "console.log('filter(>2): [' + filtered.join(', ') + ']');\n\n" +

                "// 3. 对象操作测试\n" +
                "console.log('3. 对象操作测试:');\n" +
                "var person = {\n" +
                "    name: 'Alice',\n" +
                "    age: 25,\n" +
                "    greet: function() {\n" +
                "        return 'Hello, I am ' + this.name + ', ' + this.age + ' years old.';\n" +
                "    }\n" +
                "};\n" +
                "console.log('person.greet(): ' + person.greet());\n" +
                "person.city = 'Beijing';\n" +
                "console.log('添加city后: ' + JSON.stringify(person));\n\n" +

                "// 4. 错误处理测试\n" +
                "console.log('4. 错误处理测试:');\n" +
                "try {\n" +
                "    var result = 10 / 0;\n" +
                "    console.log('10 / 0 = ' + result);\n" +
                "    throw new Error('自定义错误测试');\n" +
                "} catch (e) {\n" +
                "    console.log('捕获错误: ' + e.message);\n" +
                "} finally {\n" +
                "    console.log('finally块执行');\n" +
                "}\n\n" +

                "console.log('=== 中级测试完成 ===');";

        executeTestScript("中级测试", script);
    }

    private void runAdvancedTest() {
        Log.d(TAG, "开始高级测试");
        String script = "// 高级测试脚本\n" +
                "console.log('=== 高级测试开始 ===');\n" +
                "console.log('测试时间: ' + new Date().toString());\n\n" +

                "// 1. 闭包测试\n" +
                "console.log('1. 闭包测试:');\n" +
                "function createCounter() {\n" +
                "    var count = 0;\n" +
                "    return function() {\n" +
                "        return ++count;\n" +
                "    };\n" +
                "}\n" +
                "var counter1 = createCounter();\n" +
                "var counter2 = createCounter();\n" +
                "console.log('counter1(): ' + counter1());\n" +
                "console.log('counter1(): ' + counter1());\n" +
                "console.log('counter2(): ' + counter2());\n" +
                "console.log('counter1(): ' + counter1());\n\n" +

                "// 2. 原型链测试\n" +
                "console.log('2. 原型链测试:');\n" +
                "function Animal(name) {\n" +
                "    this.name = name;\n" +
                "}\n" +
                "Animal.prototype.speak = function() {\n" +
                "    return this.name + ' makes a sound';\n" +
                "};\n" +
                "function Dog(name, breed) {\n" +
                "    Animal.call(this, name);\n" +
                "    this.breed = breed;\n" +
                "}\n" +
                "Dog.prototype = Object.create(Animal.prototype);\n" +
                "Dog.prototype.constructor = Dog;\n" +
                "Dog.prototype.bark = function() {\n" +
                "    return this.name + ' barks!';\n" +
                "};\n" +
                "var dog = new Dog('Buddy', 'Golden Retriever');\n" +
                "console.log('dog.speak(): ' + dog.speak());\n" +
                "console.log('dog.bark(): ' + dog.bark());\n" +
                "console.log('dog instanceof Dog: ' + (dog instanceof Dog));\n" +
                "console.log('dog instanceof Animal: ' + (dog instanceof Animal));\n\n" +

                "// 3. 异步模拟测试\n" +
                "console.log('3. 异步模拟测试:');\n" +
                "function simulateAsync(callback, delay) {\n" +
                "    // 模拟异步操作\n" +
                "    var start = Date.now();\n" +
                "    while (Date.now() - start < delay) {\n" +
                "        // 忙等待模拟延迟\n" +
                "    }\n" +
                "    callback('异步操作完成');\n" +
                "}\n" +
                "console.log('开始异步操作...');\n" +
                "simulateAsync(function(result) {\n" +
                "    console.log('异步回调: ' + result);\n" +
                "}, 100);\n" +
                "console.log('异步操作已启动');\n\n" +

                "// 4. 复杂数据结构测试\n" +
                "console.log('4. 复杂数据结构测试:');\n" +
                "var complexData = {\n" +
                "    users: [\n" +
                "        {id: 1, name: 'Alice', roles: ['admin', 'user']},\n" +
                "        {id: 2, name: 'Bob', roles: ['user']},\n" +
                "        {id: 3, name: 'Charlie', roles: ['moderator', 'user']}\n" +
                "    ],\n" +
                "    settings: {\n" +
                "        theme: 'dark',\n" +
                "        notifications: true,\n" +
                "        features: {\n" +
                "            beta: false,\n" +
                "            experimental: true\n" +
                "        }\n" +
                "    }\n" +
                "};\n" +
                "console.log('用户数量: ' + complexData.users.length);\n" +
                "var admins = complexData.users.filter(function(user) {\n" +
                "    return user.roles.indexOf('admin') !== -1;\n" +
                "});\n" +
                "console.log('管理员: ' + JSON.stringify(admins));\n" +
                "console.log('主题设置: ' + complexData.settings.theme);\n" +
                "console.log('实验功能: ' + complexData.settings.features.experimental);\n\n" +

                "console.log('=== 高级测试完成 ===');";

        executeTestScript("高级测试", script);
    }

    private void runConsoleAPITest() {
        Log.d(TAG, "开始Console API测试");
        String script = "// Console API测试脚本\n" +
                "console.log('=== Console API测试开始 ===');\n" +
                "console.log('测试时间: ' + new Date().toString());\n\n" +

                "// 1. 基本日志输出测试\n" +
                "console.log('1. 基本日志输出:');\n" +
                "console.log('这是一条普通日志');\n" +
                "console.info('这是一条信息日志');\n" +
                "console.warn('这是一条警告日志');\n" +
                "console.error('这是一条错误日志');\n\n" +

                "// 2. 格式化输出测试\n" +
                "console.log('2. 格式化输出:');\n" +
                "var name = 'AutoJs6';\n" +
                "var version = '1.0.0';\n" +
                "console.log('应用: %s, 版本: %s', name, version);\n" +
                "console.log('数字格式化: %d, %f', 42, 3.14159);\n\n" +

                "// 3. 对象输出测试\n" +
                "console.log('3. 对象输出:');\n" +
                "var testObj = {\n" +
                "    string: 'hello',\n" +
                "    number: 123,\n" +
                "    boolean: true,\n" +
                "    array: [1, 2, 3],\n" +
                "    nested: {a: 1, b: 2}\n" +
                "};\n" +
                "console.log('对象:', testObj);\n" +
                "console.log('对象JSON:', JSON.stringify(testObj, null, 2));\n\n" +

                "// 4. 计时测试\n" +
                "console.log('4. 计时测试:');\n" +
                "console.time('测试计时器');\n" +
                "var sum = 0;\n" +
                "for (var i = 0; i < 1000; i++) {\n" +
                "    sum += i;\n" +
                "}\n" +
                "console.timeEnd('测试计时器');\n" +
                "console.log('计算结果: ' + sum);\n\n" +

                "// 5. 分组测试\n" +
                "console.log('5. 分组测试:');\n" +
                "console.group('外层分组');\n" +
                "console.log('外层消息1');\n" +
                "console.log('外层消息2');\n" +
                "console.group('内层分组');\n" +
                "console.log('内层消息1');\n" +
                "console.log('内层消息2');\n" +
                "console.groupEnd();\n" +
                "console.log('外层消息3');\n" +
                "console.groupEnd();\n\n" +

                "console.log('=== Console API测试完成 ===');";

        executeTestScript("Console API测试", script);
    }

    private void runUtilsAPITest() {
        Log.d(TAG, "开始Utils API测试");
        String script = "// Utils API测试脚本\n" +
                "console.log('=== Utils API测试开始 ===');\n" +
                "console.log('测试时间: ' + new Date().toString());\n\n" +

                "// 1. 字符串工具测试\n" +
                "console.log('1. 字符串工具测试:');\n" +
                "var testStr = '  Hello World  ';\n" +
                "console.log('原字符串: \"' + testStr + '\"');\n" +
                "console.log('去空格: \"' + testStr.trim() + '\"');\n" +
                "console.log('转大写: \"' + testStr.toUpperCase() + '\"');\n" +
                "console.log('转小写: \"' + testStr.toLowerCase() + '\"');\n" +
                "console.log('替换: \"' + testStr.replace('World', 'AutoJs6') + '\"');\n" +
                "console.log('分割: [' + testStr.trim().split(' ').join(', ') + ']');\n\n" +

                "// 2. 数学工具测试\n" +
                "console.log('2. 数学工具测试:');\n" +
                "var numbers = [1, 5, 3, 9, 2, 8, 4];\n" +
                "console.log('数组: [' + numbers.join(', ') + ']');\n" +
                "console.log('最大值: ' + Math.max.apply(null, numbers));\n" +
                "console.log('最小值: ' + Math.min.apply(null, numbers));\n" +
                "console.log('随机数: ' + Math.random());\n" +
                "console.log('随机整数(1-10): ' + Math.floor(Math.random() * 10 + 1));\n" +
                "console.log('圆周率: ' + Math.PI);\n" +
                "console.log('平方根(16): ' + Math.sqrt(16));\n" +
                "console.log('2的3次方: ' + Math.pow(2, 3));\n\n" +

                "// 3. 日期时间工具测试\n" +
                "console.log('3. 日期时间工具测试:');\n" +
                "var now = new Date();\n" +
                "console.log('当前时间: ' + now.toString());\n" +
                "console.log('时间戳: ' + now.getTime());\n" +
                "console.log('年份: ' + now.getFullYear());\n" +
                "console.log('月份: ' + (now.getMonth() + 1));\n" +
                "console.log('日期: ' + now.getDate());\n" +
                "console.log('小时: ' + now.getHours());\n" +
                "console.log('分钟: ' + now.getMinutes());\n" +
                "console.log('秒数: ' + now.getSeconds());\n" +
                "console.log('ISO格式: ' + now.toISOString());\n\n" +

                "// 4. JSON工具测试\n" +
                "console.log('4. JSON工具测试:');\n" +
                "var jsonObj = {\n" +
                "    name: 'AutoJs6',\n" +
                "    version: '1.0.0',\n" +
                "    features: ['scripting', 'automation', 'testing'],\n" +
                "    config: {\n" +
                "        debug: true,\n" +
                "        timeout: 5000\n" +
                "    }\n" +
                "};\n" +
                "var jsonStr = JSON.stringify(jsonObj);\n" +
                "console.log('对象转JSON: ' + jsonStr);\n" +
                "var parsedObj = JSON.parse(jsonStr);\n" +
                "console.log('JSON转对象: ' + parsedObj.name + ' v' + parsedObj.version);\n" +
                "console.log('美化JSON: ' + JSON.stringify(jsonObj, null, 2));\n\n" +

                "console.log('=== Utils API测试完成 ===');";

        executeTestScript("Utils API测试", script);
    }

    private void runSystemAPITest() {
        Log.d(TAG, "开始System API测试");
        String script = "// System API测试脚本\n" +
                "console.log('=== System API测试开始 ===');\n" +
                "console.log('测试时间: ' + new Date().toString());\n\n" +

                "// 1. 系统信息测试\n" +
                "console.log('1. 系统信息测试:');\n" +
                "console.log('用户代理: ' + (typeof navigator !== 'undefined' ? navigator.userAgent : '不可用'));\n" +
                "console.log('平台信息: ' + (typeof navigator !== 'undefined' ? navigator.platform : '不可用'));\n" +
                "console.log('语言设置: ' + (typeof navigator !== 'undefined' ? navigator.language : '不可用'));\n\n" +

                "// 2. 全局对象测试\n" +
                "console.log('2. 全局对象测试:');\n" +
                "console.log('全局对象类型: ' + typeof global);\n" +
                "console.log('window对象类型: ' + typeof window);\n" +
                "console.log('this对象类型: ' + typeof this);\n\n" +

                "// 3. 内存和性能测试\n" +
                "console.log('3. 内存和性能测试:');\n" +
                "var startTime = Date.now();\n" +
                "var largeArray = [];\n" +
                "for (var i = 0; i < 10000; i++) {\n" +
                "    largeArray.push(i * i);\n" +
                "}\n" +
                "var endTime = Date.now();\n" +
                "console.log('创建10000元素数组耗时: ' + (endTime - startTime) + 'ms');\n" +
                "console.log('数组长度: ' + largeArray.length);\n" +
                "console.log('前10个元素: [' + largeArray.slice(0, 10).join(', ') + ']');\n" +
                "largeArray = null; // 释放内存\n\n" +

                "// 4. 错误和异常测试\n" +
                "console.log('4. 错误和异常测试:');\n" +
                "var errorTypes = ['Error', 'TypeError', 'ReferenceError', 'SyntaxError'];\n" +
                "errorTypes.forEach(function(errorType) {\n" +
                "    try {\n" +
                "        if (errorType === 'Error') {\n" +
                "            throw new Error('测试Error');\n" +
                "        } else if (errorType === 'TypeError') {\n" +
                "            var obj = null;\n" +
                "            obj.someMethod();\n" +
                "        } else if (errorType === 'ReferenceError') {\n" +
                "            console.log(undefinedVariable);\n" +
                "        }\n" +
                "    } catch (e) {\n" +
                "        console.log('捕获' + errorType + ': ' + e.message);\n" +
                "    }\n" +
                "});\n\n" +

                "// 5. 垃圾回收测试\n" +
                "console.log('5. 垃圾回收测试:');\n" +
                "function createObjects() {\n" +
                "    var objects = [];\n" +
                "    for (var i = 0; i < 1000; i++) {\n" +
                "        objects.push({\n" +
                "            id: i,\n" +
                "            data: new Array(100).fill(i),\n" +
                "            timestamp: Date.now()\n" +
                "        });\n" +
                "    }\n" +
                "    return objects.length;\n" +
                "}\n" +
                "var objectCount = createObjects();\n" +
                "console.log('创建对象数量: ' + objectCount);\n" +
                "console.log('建议进行垃圾回收...');\n\n" +

                "console.log('=== System API测试完成 ===');";

        executeTestScript("System API测试", script);
    }

    private void runAllAPIsTest() {
        Log.d(TAG, "开始全部API测试");
        String script = "// 全部API综合测试脚本\n" +
                "console.log('=== 全部API综合测试开始 ===');\n" +
                "console.log('测试时间: ' + new Date().toString());\n" +
                "console.log('这将运行所有可用的API测试...');\n\n" +

                "// 测试计数器\n" +
                "var testCount = 0;\n" +
                "var passCount = 0;\n" +
                "var failCount = 0;\n\n" +

                "function runTest(testName, testFunc) {\n" +
                "    testCount++;\n" +
                "    console.log('\\n--- 测试 ' + testCount + ': ' + testName + ' ---');\n" +
                "    try {\n" +
                "        var result = testFunc();\n" +
                "        if (result !== false) {\n" +
                "            console.log('✓ ' + testName + ' 通过');\n" +
                "            passCount++;\n" +
                "        } else {\n" +
                "            console.log('✗ ' + testName + ' 失败');\n" +
                "            failCount++;\n" +
                "        }\n" +
                "    } catch (e) {\n" +
                "        console.log('✗ ' + testName + ' 异常: ' + e.message);\n" +
                "        failCount++;\n" +
                "    }\n" +
                "}\n\n" +

                "// 1. 基础语法测试\n" +
                "runTest('变量声明和赋值', function() {\n" +
                "    var a = 1, b = 'test', c = true, d = [1,2,3], e = {x:1};\n" +
                "    return a === 1 && b === 'test' && c === true && d.length === 3 && e.x === 1;\n" +
                "});\n\n" +

                "runTest('运算符测试', function() {\n" +
                "    return (5 + 3 === 8) && (10 - 4 === 6) && (3 * 4 === 12) && (15 / 3 === 5);\n" +
                "});\n\n" +

                "runTest('条件语句测试', function() {\n" +
                "    var result = '';\n" +
                "    if (true) result += 'a';\n" +
                "    if (false) result += 'b'; else result += 'c';\n" +
                "    return result === 'ac';\n" +
                "});\n\n" +

                "runTest('循环语句测试', function() {\n" +
                "    var sum = 0;\n" +
                "    for (var i = 1; i <= 5; i++) sum += i;\n" +
                "    return sum === 15;\n" +
                "});\n\n" +

                "// 2. 函数测试\n" +
                "runTest('函数定义和调用', function() {\n" +
                "    function add(x, y) { return x + y; }\n" +
                "    return add(3, 4) === 7;\n" +
                "});\n\n" +

                "runTest('匿名函数测试', function() {\n" +
                "    var multiply = function(x, y) { return x * y; };\n" +
                "    return multiply(3, 4) === 12;\n" +
                "});\n\n" +

                "runTest('闭包测试', function() {\n" +
                "    function createAdder(x) {\n" +
                "        return function(y) { return x + y; };\n" +
                "    }\n" +
                "    var add5 = createAdder(5);\n" +
                "    return add5(3) === 8;\n" +
                "});\n\n" +

                "// 3. 对象和数组测试\n" +
                "runTest('数组操作测试', function() {\n" +
                "    var arr = [1, 2, 3];\n" +
                "    arr.push(4);\n" +
                "    var doubled = arr.map(function(x) { return x * 2; });\n" +
                "    return arr.length === 4 && doubled[0] === 2 && doubled[3] === 8;\n" +
                "});\n\n" +

                "runTest('对象操作测试', function() {\n" +
                "    var obj = {a: 1, b: 2};\n" +
                "    obj.c = 3;\n" +
                "    delete obj.b;\n" +
                "    return obj.a === 1 && obj.c === 3 && obj.b === undefined;\n" +
                "});\n\n" +

                "// 4. 字符串测试\n" +
                "runTest('字符串操作测试', function() {\n" +
                "    var str = 'Hello World';\n" +
                "    return str.length === 11 && str.indexOf('World') === 6 && str.toUpperCase() === 'HELLO WORLD';\n" +
                "});\n\n" +

                "// 5. 正则表达式测试\n" +
                "runTest('正则表达式测试', function() {\n" +
                "    var regex = /\\d+/;\n" +
                "    return regex.test('abc123def') && 'abc123def'.match(/\\d+/)[0] === '123';\n" +
                "});\n\n" +

                "// 6. JSON测试\n" +
                "runTest('JSON序列化测试', function() {\n" +
                "    var obj = {name: 'test', value: 42};\n" +
                "    var json = JSON.stringify(obj);\n" +
                "    var parsed = JSON.parse(json);\n" +
                "    return parsed.name === 'test' && parsed.value === 42;\n" +
                "});\n\n" +

                "// 7. 错误处理测试\n" +
                "runTest('异常处理测试', function() {\n" +
                "    try {\n" +
                "        throw new Error('test error');\n" +
                "    } catch (e) {\n" +
                "        return e.message === 'test error';\n" +
                "    }\n" +
                "    return false;\n" +
                "});\n\n" +

                "// 输出测试结果\n" +
                "console.log('\\n=== 测试结果汇总 ===');\n" +
                "console.log('总测试数: ' + testCount);\n" +
                "console.log('通过数: ' + passCount);\n" +
                "console.log('失败数: ' + failCount);\n" +
                "console.log('成功率: ' + Math.round((passCount / testCount) * 100) + '%');\n" +
                "console.log('=== 全部API综合测试完成 ===');";

        executeTestScript("全部API测试", script);
    }

    private void runPerformanceTest() {
        Log.d(TAG, "开始性能测试");
        String script = "// 性能测试脚本\n" +
                "console.log('=== 性能测试开始 ===');\n" +
                "console.log('测试时间: ' + new Date().toString());\n\n" +

                "function measureTime(name, func) {\n" +
                "    console.log('\\n--- ' + name + ' ---');\n" +
                "    var start = Date.now();\n" +
                "    var result = func();\n" +
                "    var end = Date.now();\n" +
                "    var duration = end - start;\n" +
                "    console.log('耗时: ' + duration + 'ms');\n" +
                "    console.log('结果: ' + result);\n" +
                "    return duration;\n" +
                "}\n\n" +

                "// 1. 循环性能测试\n" +
                "measureTime('for循环(100000次)', function() {\n" +
                "    var sum = 0;\n" +
                "    for (var i = 0; i < 100000; i++) {\n" +
                "        sum += i;\n" +
                "    }\n" +
                "    return sum;\n" +
                "});\n\n" +

                "measureTime('while循环(100000次)', function() {\n" +
                "    var sum = 0;\n" +
                "    var i = 0;\n" +
                "    while (i < 100000) {\n" +
                "        sum += i;\n" +
                "        i++;\n" +
                "    }\n" +
                "    return sum;\n" +
                "});\n\n" +

                "// 2. 数组操作性能测试\n" +
                "measureTime('数组push(10000次)', function() {\n" +
                "    var arr = [];\n" +
                "    for (var i = 0; i < 10000; i++) {\n" +
                "        arr.push(i);\n" +
                "    }\n" +
                "    return arr.length;\n" +
                "});\n\n" +

                "measureTime('数组map操作(10000元素)', function() {\n" +
                "    var arr = [];\n" +
                "    for (var i = 0; i < 10000; i++) arr.push(i);\n" +
                "    var mapped = arr.map(function(x) { return x * 2; });\n" +
                "    return mapped.length;\n" +
                "});\n\n" +

                "measureTime('数组filter操作(10000元素)', function() {\n" +
                "    var arr = [];\n" +
                "    for (var i = 0; i < 10000; i++) arr.push(i);\n" +
                "    var filtered = arr.filter(function(x) { return x % 2 === 0; });\n" +
                "    return filtered.length;\n" +
                "});\n\n" +

                "// 3. 字符串操作性能测试\n" +
                "measureTime('字符串拼接(10000次)', function() {\n" +
                "    var str = '';\n" +
                "    for (var i = 0; i < 10000; i++) {\n" +
                "        str += 'a';\n" +
                "    }\n" +
                "    return str.length;\n" +
                "});\n\n" +

                "measureTime('数组join拼接(10000次)', function() {\n" +
                "    var arr = [];\n" +
                "    for (var i = 0; i < 10000; i++) {\n" +
                "        arr.push('a');\n" +
                "    }\n" +
                "    return arr.join('').length;\n" +
                "});\n\n" +

                "// 4. 对象操作性能测试\n" +
                "measureTime('对象属性访问(100000次)', function() {\n" +
                "    var obj = {a: 1, b: 2, c: 3, d: 4, e: 5};\n" +
                "    var sum = 0;\n" +
                "    for (var i = 0; i < 100000; i++) {\n" +
                "        sum += obj.a + obj.b + obj.c + obj.d + obj.e;\n" +
                "    }\n" +
                "    return sum;\n" +
                "});\n\n" +

                "// 5. 函数调用性能测试\n" +
                "measureTime('函数调用(100000次)', function() {\n" +
                "    function add(a, b) { return a + b; }\n" +
                "    var sum = 0;\n" +
                "    for (var i = 0; i < 100000; i++) {\n" +
                "        sum = add(sum, 1);\n" +
                "    }\n" +
                "    return sum;\n" +
                "});\n\n" +

                "console.log('=== 性能测试完成 ===');";

        executeTestScript("性能测试", script);
    }

    private void runStressTest() {
        Log.d(TAG, "开始压力测试");
        String script = "// 压力测试脚本\n" +
                "console.log('=== 压力测试开始 ===');\n" +
                "console.log('测试时间: ' + new Date().toString());\n" +
                "console.log('警告: 这是一个高强度测试，可能会消耗较多资源');\n\n" +

                "var testResults = [];\n\n" +

                "function stressTest(name, iterations, testFunc) {\n" +
                "    console.log('\\n--- ' + name + ' (' + iterations + '次迭代) ---');\n" +
                "    var start = Date.now();\n" +
                "    var errors = 0;\n" +
                "    \n" +
                "    for (var i = 0; i < iterations; i++) {\n" +
                "        try {\n" +
                "            testFunc(i);\n" +
                "        } catch (e) {\n" +
                "            errors++;\n" +
                "            if (errors <= 5) {\n" +
                "                console.log('错误 ' + errors + ': ' + e.message);\n" +
                "            }\n" +
                "        }\n" +
                "        \n" +
                "        // 每1000次迭代报告一次进度\n" +
                "        if (i > 0 && i % 1000 === 0) {\n" +
                "            console.log('进度: ' + i + '/' + iterations + ' (' + Math.round(i/iterations*100) + '%)');\n" +
                "        }\n" +
                "    }\n" +
                "    \n" +
                "    var end = Date.now();\n" +
                "    var duration = end - start;\n" +
                "    var result = {\n" +
                "        name: name,\n" +
                "        iterations: iterations,\n" +
                "        duration: duration,\n" +
                "        errors: errors,\n" +
                "        rate: Math.round(iterations / duration * 1000)\n" +
                "    };\n" +
                "    \n" +
                "    testResults.push(result);\n" +
                "    console.log('完成: ' + duration + 'ms, 错误: ' + errors + ', 速率: ' + result.rate + ' ops/sec');\n" +
                "    return result;\n" +
                "}\n\n" +

                "// 1. 大量变量创建和销毁\n" +
                "stressTest('变量创建销毁', 50000, function(i) {\n" +
                "    var obj = {\n" +
                "        id: i,\n" +
                "        name: 'object_' + i,\n" +
                "        data: new Array(10).fill(i),\n" +
                "        timestamp: Date.now()\n" +
                "    };\n" +
                "    obj = null;\n" +
                "});\n\n" +

                "// 2. 大量函数调用\n" +
                "stressTest('函数调用', 100000, function(i) {\n" +
                "    function calculate(x) {\n" +
                "        return Math.sqrt(x * x + 1) / (x + 1);\n" +
                "    }\n" +
                "    calculate(i);\n" +
                "});\n\n" +

                "// 3. 大量字符串操作\n" +
                "stressTest('字符串操作', 10000, function(i) {\n" +
                "    var str = 'test_string_' + i;\n" +
                "    str = str.toUpperCase().toLowerCase().replace('test', 'stress');\n" +
                "    str.split('_').join('-');\n" +
                "});\n\n" +

                "// 4. 大量数组操作\n" +
                "stressTest('数组操作', 5000, function(i) {\n" +
                "    var arr = [];\n" +
                "    for (var j = 0; j < 100; j++) {\n" +
                "        arr.push(j * i);\n" +
                "    }\n" +
                "    arr.map(function(x) { return x * 2; })\n" +
                "       .filter(function(x) { return x % 2 === 0; })\n" +
                "       .reduce(function(sum, x) { return sum + x; }, 0);\n" +
                "});\n\n" +

                "// 5. 递归调用测试\n" +
                "stressTest('递归调用', 1000, function(i) {\n" +
                "    function fibonacci(n) {\n" +
                "        if (n <= 1) return n;\n" +
                "        if (n > 20) return 0; // 防止栈溢出\n" +
                "        return fibonacci(n - 1) + fibonacci(n - 2);\n" +
                "    }\n" +
                "    fibonacci(i % 15 + 1);\n" +
                "});\n\n" +

                "// 6. JSON序列化/反序列化\n" +
                "stressTest('JSON操作', 10000, function(i) {\n" +
                "    var obj = {\n" +
                "        id: i,\n" +
                "        data: {\n" +
                "            values: [i, i*2, i*3],\n" +
                "            meta: {\n" +
                "                created: Date.now(),\n" +
                "                type: 'stress_test'\n" +
                "            }\n" +
                "        }\n" +
                "    };\n" +
                "    var json = JSON.stringify(obj);\n" +
                "    JSON.parse(json);\n" +
                "});\n\n" +

                "// 输出压力测试结果\n" +
                "console.log('\\n=== 压力测试结果汇总 ===');\n" +
                "var totalDuration = 0;\n" +
                "var totalIterations = 0;\n" +
                "var totalErrors = 0;\n" +
                "\n" +
                "testResults.forEach(function(result) {\n" +
                "    console.log(result.name + ': ' + result.iterations + '次, ' + \n" +
                "                result.duration + 'ms, ' + result.errors + '错误, ' + \n" +
                "                result.rate + ' ops/sec');\n" +
                "    totalDuration += result.duration;\n" +
                "    totalIterations += result.iterations;\n" +
                "    totalErrors += result.errors;\n" +
                "});\n\n" +

                "console.log('\\n总计:');\n" +
                "console.log('总迭代次数: ' + totalIterations);\n" +
                "console.log('总耗时: ' + totalDuration + 'ms');\n" +
                "console.log('总错误数: ' + totalErrors);\n" +
                "console.log('平均速率: ' + Math.round(totalIterations / totalDuration * 1000) + ' ops/sec');\n" +
                "console.log('错误率: ' + (totalErrors / totalIterations * 100).toFixed(2) + '%');\n" +
                "console.log('=== 压力测试完成 ===');";

        executeTestScript("压力测试", script);
    }

    /**
     * 执行测试脚本的通用方法
     */
    private void executeTestScript(String testName, String script) {
        Log.d(TAG, "执行测试: " + testName);

        // 更新UI显示当前测试
        etScriptName.setText(testName);
        etScriptContent.setText(script);

        // 显示开始测试的提示
        showToast("开始执行: " + testName);

        // 执行脚本
        executeScript();
    }

    private void showToast(String message) {
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        }
    }
    
    @Override
    public void onResume() {
        super.onResume();
        updateUI();
    }
    
    // ========== 实用脚本方法 ==========

    /**
     * 运行今日头条浏览脚本
     */
    private void runToutiaoScript() {
        Log.d(TAG, "开始运行今日头条浏览脚本");

        String scriptContent = readScriptFromResource(R.raw.toutiao_browsing_script);
        String scriptName = "今日头条浏览脚本";

        // 更新UI显示当前脚本
        etScriptName.setText(scriptName);
        etScriptContent.setText(scriptContent);

        // 显示开始执行的提示
        showToast("开始执行: " + scriptName);
        tvScriptStatus.setText("正在执行: " + scriptName);

        // 执行脚本
        executeScript();
    }

    /**
     * 使用AutoJs6Module执行今日头条脚本
     */
    private void runToutiaoScriptWithAutoJs6() {
        String scriptContent = readScriptFromResource(R.raw.toutiao_browsing_script);
        String scriptName = "今日头条浏览脚本";

        // 更新UI显示当前脚本
        etScriptName.setText(scriptName);
        etScriptContent.setText(scriptContent);

        // 显示开始执行的提示
        showToast("开始执行: " + scriptName);

        // 执行脚本
        executeScript();
    }

    /**
     * 运行AutoJS功能测试脚本
     */
    private void runAutoJSTest() {
        Log.d(TAG, "开始运行AutoJS功能测试");

        // 首先检查AutoJS框架是否可用
        AutoJsFramework framework = AutoJsFramework.getInstance();
        if (!framework.isInitialized()) {
            Log.w(TAG, "AutoJS框架未初始化，尝试使用AutoJs6Module执行");
            // 如果AutoJS框架不可用，使用AutoJs6Module执行
            runAutoJSTestWithAutoJs6();
            return;
        }

        String scriptContent = readScriptFromResource(R.raw.test_script);
        String scriptName = "AutoJS功能测试脚本";

        framework.executeScript(scriptContent, scriptName, new AutoJsFramework.ScriptExecutionCallback() {
            @Override
            public void onStart(String scriptName) {
                Log.d(TAG, "AutoJS测试脚本开始执行: " + scriptName);
                uiHandler.post(() -> {
                    showToast("AutoJS测试脚本开始执行");
                    tvScriptStatus.setText("正在执行: " + scriptName);
                });
            }

            @Override
            public void onSuccess(String scriptName, String result) {
                Log.d(TAG, "AutoJS测试脚本执行成功: " + scriptName);
                uiHandler.post(() -> {
                    showToast("AutoJS测试脚本执行成功");
                    tvScriptStatus.setText("脚本执行成功: " + scriptName);
                });
            }

            @Override
            public void onError(String scriptName, String error) {
                Log.e(TAG, "AutoJS测试脚本执行失败: " + scriptName + ", 错误: " + error);
                uiHandler.post(() -> {
                    showToast("AutoJS测试脚本执行失败: " + error);
                    tvScriptStatus.setText("脚本执行失败: " + error);
                });
            }
        });
    }

    /**
     * 使用AutoJs6Module执行AutoJS测试脚本
     */
    private void runAutoJSTestWithAutoJs6() {
        String scriptContent = readScriptFromResource(R.raw.test_script);
        String scriptName = "AutoJS功能测试脚本";

        // 更新UI显示当前脚本
        etScriptName.setText(scriptName);
        etScriptContent.setText(scriptContent);

        // 显示开始执行的提示
        showToast("开始执行: " + scriptName);

        // 执行脚本
        executeScript();
    }

    /**
     * 从资源文件读取脚本内容
     */
    private String readScriptFromResource(int resourceId) {
        try {
            InputStream inputStream = getResources().openRawResource(resourceId);
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            StringBuilder stringBuilder = new StringBuilder();
            String line;

            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line).append("\n");
            }

            reader.close();
            inputStream.close();

            return stringBuilder.toString();
        } catch (IOException e) {
            Log.e(TAG, "Error reading script file", e);
            return "console.log('脚本文件读取失败: " + e.getMessage() + "');";
        }
    }

    @Override
    public int getIconResourceId() {
        return R.drawable.ic_code;
    }
}

<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2022 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<animated-vector xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:aapt="http://schemas.android.com/aapt"
  xmlns:tools="http://schemas.android.com/tools"
  android:drawable="@drawable/mtrl_switch_thumb_unchecked"
  tools:ignore="NewApi">

  <set
    android:interpolator="@interpolator/m3_sys_motion_easing_emphasized"
    android:shareInterpolator="true">
    <target android:name="@string/mtrl_switch_thumb_path_name">
      <aapt:attr name="android:animation">
        <objectAnimator
          android:duration="@integer/mtrl_switch_thumb_post_morphing_duration"
          android:propertyName="pathData"
          android:valueFrom="@string/mtrl_switch_thumb_path_unchecked"
          android:valueTo="@string/mtrl_switch_thumb_path_morphing"
          android:valueType="pathType" />
      </aapt:attr>
    </target>
    <target android:name="@string/mtrl_switch_thumb_path_name">
      <aapt:attr name="android:animation">
        <objectAnimator
          android:startOffset="@integer/mtrl_switch_thumb_post_morphing_duration"
          android:duration="@integer/mtrl_switch_thumb_pre_morphing_duration"
          android:propertyName="pathData"
          android:valueFrom="@string/mtrl_switch_thumb_path_morphing"
          android:valueTo="@string/mtrl_switch_thumb_path_checked"
          android:valueType="pathType" />
      </aapt:attr>
    </target>
  </set>
</animated-vector>

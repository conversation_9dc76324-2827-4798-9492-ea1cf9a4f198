<?xml version="1.0" encoding="utf-8"?>
  <!--
      Copyright 2019 The Android Open Source Project

      Licensed under the Apache License, Version 2.0 (the "License");
      you may not use this file except in compliance with the License.
      You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

      Unless required by applicable law or agreed to in writing, software
      distributed under the License is distributed on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
      See the License for the specific language governing permissions and
      limitations under the License.
  -->

<selector
  xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto">

  <!-- Dragged state -->
  <item
    android:state_enabled="true"
    app:state_dragged="true">
    <set>
      <objectAnimator
        android:duration="@integer/mtrl_card_anim_duration_ms"
        android:propertyName="translationZ"
        android:valueTo="@dimen/mtrl_card_dragged_z"
        android:startDelay="@integer/mtrl_card_anim_delay_ms"
        android:interpolator="@interpolator/mtrl_fast_out_slow_in"
        android:valueType="floatType"/>
    </set>
  </item>

  <!-- Base state (enabled, not Dragged) -->
  <item android:state_enabled="true">
    <set>
      <objectAnimator
        android:duration="@integer/mtrl_card_anim_duration_ms"
        android:propertyName="translationZ"
        android:valueTo="0dp"
        android:interpolator="@anim/mtrl_card_lowers_interpolator"
        android:valueType="floatType"/>
    </set>
  </item>

</selector>

@echo off
chcp 65001 >nul

REM AutoJs6 测试脚本 (Windows版本)
REM 用于构建和测试AutoJs6功能

echo === AutoJs6 测试脚本开始 ===

REM 检查环境
echo 1. 检查环境...
if not exist "gradlew.bat" (
    echo 错误: 未找到gradlew.bat文件，请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

REM 清理项目
echo 2. 清理项目...
call gradlew.bat clean

REM 构建项目
echo 3. 构建项目...
call gradlew.bat build

if errorlevel 1 (
    echo 错误: 项目构建失败
    pause
    exit /b 1
)

echo ✓ 项目构建成功

REM 运行单元测试
echo 4. 运行单元测试...
call gradlew.bat test

if errorlevel 1 (
    echo 警告: 单元测试失败，但继续执行
) else (
    echo ✓ 单元测试通过
)

REM 检查设备连接
echo 5. 检查设备连接...
adb devices

REM 检查是否有设备连接
adb devices | findstr "device" | findstr /v "List of devices" >nul
if errorlevel 1 (
    echo 警告: 未检测到连接的设备，跳过设备测试
    goto summary
)

REM 安装应用
echo 6. 安装应用到设备...
call gradlew.bat installDebug

if errorlevel 1 (
    echo 警告: 应用安装失败
    goto summary
)

echo ✓ 应用安装成功

REM 运行集成测试
echo 7. 运行集成测试...
call gradlew.bat connectedAndroidTest

if errorlevel 1 (
    echo 警告: 集成测试失败
) else (
    echo ✓ 集成测试通过
)

:summary
echo.
echo === 测试总结 ===
echo ✓ 项目构建: 成功
echo ✓ 单元测试: 完成

adb devices | findstr "device" | findstr /v "List of devices" >nul
if errorlevel 1 (
    echo - 设备测试: 跳过（无设备）
) else (
    echo ✓ 设备测试: 完成
)

echo.
echo === AutoJs6 测试脚本完成 ===
echo.
echo 如何手动测试AutoJs6功能：
echo 1. 启动应用
echo 2. 切换到'JS脚本执行器'标签页
echo 3. 点击'执行脚本'按钮测试默认脚本
echo 4. 观察控制台输出和Toast消息
echo 5. 检查'运行中的脚本'计数
echo.
echo 测试脚本位置：
echo - test_scripts/simple_test.js - 基础功能测试
echo - test_scripts/autojs6_framework_test.js - 框架深度测试
echo - test_scripts/debug_test.js - 调试测试
echo.

pause

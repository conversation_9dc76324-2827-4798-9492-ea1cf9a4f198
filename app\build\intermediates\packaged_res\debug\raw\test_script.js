// 测试脚本
console.log('🧪 === AutoJS 测试脚本开始执行 ===');

// 基础功能测试
function testBasicFunctions() {
    console.log('📋 开始基础功能测试...');
    
    try {
        // 测试设备信息
        console.log('📱 设备宽度: ' + device.width);
        console.log('📱 设备高度: ' + device.height);
        console.log('📱 当前包名: ' + currentPackage());
        
        // 测试等待功能
        console.log('⏱️ 测试等待功能...');
        sleep(1000);
        console.log('✅ 等待功能正常');
        
        // 测试无障碍服务
        if (auto.service) {
            console.log('✅ 无障碍服务已启用');
        } else {
            console.log('❌ 无障碍服务未启用');
        }
        
        console.log('✅ 基础功能测试完成');
        return true;
        
    } catch (e) {
        console.log('❌ 基础功能测试失败: ' + e.message);
        return false;
    }
}

// 界面元素测试
function testUIElements() {
    console.log('🎯 开始界面元素测试...');
    
    try {
        // 查找一些常见的界面元素
        var elements = [
            text('设置'),
            text('返回'),
            className('android.widget.Button'),
            className('android.widget.TextView')
        ];
        
        for (var i = 0; i < elements.length; i++) {
            try {
                var found = elements[i].findOne(1000);
                if (found && found.exists()) {
                    console.log('✅ 找到元素: ' + elements[i]);
                } else {
                    console.log('ℹ️ 未找到元素: ' + elements[i]);
                }
            } catch (e) {
                console.log('⚠️ 元素查找异常: ' + e.message);
            }
        }
        
        console.log('✅ 界面元素测试完成');
        return true;
        
    } catch (e) {
        console.log('❌ 界面元素测试失败: ' + e.message);
        return false;
    }
}

// 手势操作测试
function testGestures() {
    console.log('👆 开始手势操作测试...');
    
    try {
        var centerX = device.width / 2;
        var centerY = device.height / 2;
        
        console.log('🎯 测试点击操作...');
        // 不实际点击，只是测试坐标计算
        console.log('✅ 点击坐标计算正常: (' + centerX + ', ' + centerY + ')');
        
        console.log('📱 测试滑动操作...');
        // 不实际滑动，只是测试坐标计算
        var startY = device.height * 0.8;
        var endY = device.height * 0.3;
        console.log('✅ 滑动坐标计算正常: (' + centerX + ', ' + startY + ') -> (' + centerX + ', ' + endY + ')');
        
        console.log('✅ 手势操作测试完成');
        return true;
        
    } catch (e) {
        console.log('❌ 手势操作测试失败: ' + e.message);
        return false;
    }
}

// 主测试函数
function runTests() {
    var startTime = new Date().getTime();
    
    console.log('🚀 === 开始 AutoJS 功能测试 ===');
    
    var results = {
        basic: false,
        ui: false,
        gestures: false
    };
    
    // 运行各项测试
    results.basic = testBasicFunctions();
    results.ui = testUIElements();
    results.gestures = testGestures();
    
    // 统计结果
    var passed = 0;
    var total = 0;
    for (var key in results) {
        total++;
        if (results[key]) {
            passed++;
        }
    }
    
    var endTime = new Date().getTime();
    var duration = Math.round((endTime - startTime) / 1000);
    
    console.log('📊 === 测试结果统计 ===');
    console.log('✅ 通过测试: ' + passed + '/' + total);
    console.log('⏱️ 测试耗时: ' + duration + '秒');
    
    if (passed === total) {
        console.log('🎉 所有测试通过！AutoJS 功能正常');
    } else {
        console.log('⚠️ 部分测试失败，请检查 AutoJS 配置');
    }
    
    console.log('🏁 === AutoJS 功能测试完成 ===');
    
    return passed === total;
}

// 执行测试
runTests();

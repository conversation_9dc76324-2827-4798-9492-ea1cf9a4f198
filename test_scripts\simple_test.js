// AutoJs6 Module 测试脚本 - 更新版本
console.log("=== AutoJs6 Module 功能测试开始 ===");

// 1. 基础输出测试
console.log("1. 基础输出测试");
console.log("Hello AutoJs6 Module!");

// 2. 测试utils工具函数
console.log("2. Utils工具函数测试");
if (typeof utils !== 'undefined') {
    utils.log("这是通过utils.log输出的消息");
    console.log("✓ utils对象可用");
} else {
    console.log("✗ utils对象未定义");
}

// 3. 变量和计算测试
console.log("3. 变量和计算测试");
var a = 10;
var b = 20;
var result = a + b;
console.log("计算结果: " + a + " + " + b + " = " + result);
if (typeof utils !== 'undefined') {
    utils.toast("📊 计算结果: " + a + " + " + b + " = " + result);
}
console.log("✓ 基础运算正常");

// 4. 循环测试
console.log("4. 循环控制测试");
for (var i = 1; i <= 3; i++) {
    console.log("  循环第 " + i + " 次");
}
console.log("✓ for循环正常");

// 5. 函数定义和调用测试
console.log("5. 函数定义和调用测试");
function greet(name) {
    return "Hello, " + name + "!";
}

function calculate(x, y) {
    return x * y;
}

var greeting = greet("AutoJs6");
var multiplication = calculate(6, 7);
console.log("  " + greeting);
console.log("  6 × 7 = " + multiplication);
if (typeof utils !== 'undefined') {
    utils.toast("🔧 函数测试: 6 × 7 = " + multiplication);
}
console.log("✓ 函数定义和调用正常");

// 6. 对象操作测试
console.log("6. 对象操作测试");
var person = {
    name: "张三",
    age: 25,
    city: "北京",
    sayHello: function() {
        return "我是" + this.name + "，今年" + this.age + "岁，来自" + this.city;
    },
    getInfo: function() {
        return {
            fullName: this.name,
            birthYear: new Date().getFullYear() - this.age
        };
    }
};

console.log("  " + person.sayHello());
var info = person.getInfo();
console.log("  出生年份: " + info.birthYear);
console.log("✓ 对象操作正常");

// 7. 数组操作测试
console.log("7. 数组操作测试");
var numbers = [1, 2, 3, 4, 5];
var sum = 0;
var product = 1;

for (var i = 0; i < numbers.length; i++) {
    sum += numbers[i];
    product *= numbers[i];
}

console.log("  数组: [" + numbers.join(", ") + "]");
console.log("  求和: " + sum);
console.log("  求积: " + product);
if (typeof utils !== 'undefined') {
    utils.toast("📈 数组求和: " + sum + ", 求积: " + product);
}
console.log("✓ 数组操作正常");

// 8. 字符串操作测试
console.log("8. 字符串操作测试");
var text = "AutoJs6 Module Test";
console.log("  原文本: " + text);
console.log("  转大写: " + text.toUpperCase());
console.log("  转小写: " + text.toLowerCase());
console.log("  文本长度: " + text.length);
console.log("  包含'Module': " + (text.indexOf("Module") !== -1));
console.log("✓ 字符串操作正常");

// 9. 日期和时间测试
console.log("9. 日期和时间测试");
var now = new Date();
console.log("  当前时间: " + now.toString());
console.log("  时间戳: " + now.getTime());
console.log("  年份: " + now.getFullYear());
console.log("✓ 日期时间操作正常");

// 10. 条件判断测试
console.log("10. 条件判断测试");
var score = 85;
var grade;

if (score >= 90) {
    grade = "A";
} else if (score >= 80) {
    grade = "B";
} else if (score >= 70) {
    grade = "C";
} else {
    grade = "D";
}

console.log("  分数: " + score + ", 等级: " + grade);
if (typeof utils !== 'undefined') {
    utils.toast("🎯 条件判断: 分数 " + score + " = 等级 " + grade);
}
console.log("✓ 条件判断正常");

// 11. 异常处理测试
console.log("11. 异常处理测试");
try {
    var testVar = undefinedVariable; // 这会引发错误
} catch (e) {
    console.log("  捕获到异常: " + e.message);
    if (typeof utils !== 'undefined') {
        utils.toast("🛡️ 异常处理: 成功捕获错误");
    }
    console.log("✓ 异常处理正常");
}

// 12. 最终测试结果
console.log("=== 测试总结 ===");
var testResults = {
    totalTests: 11,
    passedTests: 11,
    failedTests: 0,
    successRate: "100%"
};

console.log("总测试数: " + testResults.totalTests);
console.log("通过测试: " + testResults.passedTests);
console.log("失败测试: " + testResults.failedTests);
console.log("成功率: " + testResults.successRate);
if (typeof utils !== 'undefined') {
    utils.toast("🎉 测试完成: " + testResults.successRate + " 通过率");
}

console.log("=== AutoJs6 Module 功能测试完成 ===");

// 返回测试结果
"AutoJs6 Module 测试完成 - 所有 " + testResults.totalTests + " 项测试通过，成功率: " + testResults.successRate;

package com.bm.atool.ui;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;


import com.bm.atool.R;
import com.bm.atool.Sys;
import com.bm.atool.service.SocketService;
import com.bm.atool.service.WatchDogService;
import com.bm.autojs6.module.AutoJs6Module;
import com.bm.autojs6.module.model.Script;
import com.bm.autojs6.module.model.ScriptResult;

import java.util.List;
import java.io.InputStream;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.IOException;

public class DebugFragment extends BaseFragment{
    private static final String TAG = "DebugFragment";
    public DebugFragment(){
        setTitle("DEBUG");
    }
    private Button btnStopSocket;
    private Button btnToutiaoScript;
    private Button btnTestScript;
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Initialize view
        View view =inflater.inflate(R.layout.fragment_debug, container, false);
        btnStopSocket = view.findViewById(R.id.btnStopSocket);
        btnToutiaoScript = view.findViewById(R.id.btnToutiaoScript);
        btnTestScript = view.findViewById(R.id.btnTestScript);

        btnStopSocket.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "Stopping all services from DebugFragment");
                Sys.stop();
                Log.d(TAG, "Sys.stop() called.");
            }
        });

        btnTestScript.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "Starting AutoJS test script");
                runTestScript();
            }
        });

        btnToutiaoScript.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "Starting Toutiao browsing script");
                runToutiaoScript();
            }
        });


        return view;
    }

    private void runToutiaoScript() {
        AutoJs6Module autoJs6Module = AutoJs6Module.getInstance();

        if (!autoJs6Module.isInitialized()) {
            Log.e(TAG, "AutoJs6Module not initialized, initializing now...");
            autoJs6Module.initialize(getContext());
        }

        String scriptContent = getToutiaoScript();
        String scriptName = "今日头条浏览脚本";

        Log.d(TAG, "开始执行今日头条脚本: " + scriptName);

        // 使用AutoJs6Module异步执行脚本
        Script script = new Script(scriptName, scriptContent);
        autoJs6Module.executeScriptAsync(script)
            .thenAccept(result -> {
                if (result.isSuccess()) {
                    Log.d(TAG, "脚本执行成功: " + scriptName + ", 结果: " + result.getResult());
                } else {
                    Log.e(TAG, "脚本执行失败: " + scriptName + ", 错误: " + result.getError());
                }
            })
            .exceptionally(throwable -> {
                Log.e(TAG, "脚本执行异常: " + scriptName, throwable);
                return null;
            });
    }

    private void runTestScript() {
        AutoJs6Module autoJs6Module = AutoJs6Module.getInstance();

        if (!autoJs6Module.isInitialized()) {
            Log.e(TAG, "AutoJs6Module not initialized, initializing now...");
            autoJs6Module.initialize(getContext());
        }

        String scriptContent = getTestScript();
        String scriptName = "AutoJS测试脚本";

        Log.d(TAG, "开始执行测试脚本: " + scriptName);

        // 使用AutoJs6Module异步执行脚本
        Script script = new Script(scriptName, scriptContent);
        autoJs6Module.executeScriptAsync(script)
            .thenAccept(result -> {
                if (result.isSuccess()) {
                    Log.d(TAG, "测试脚本执行成功: " + scriptName + ", 结果: " + result.getResult());
                } else {
                    Log.e(TAG, "测试脚本执行失败: " + scriptName + ", 错误: " + result.getError());
                }
            })
            .exceptionally(throwable -> {
                Log.e(TAG, "测试脚本执行异常: " + scriptName, throwable);
                return null;
            });
    }

    private String getTestScript() {
        return readScriptFromResource(R.raw.test_script);
    }

    private String getToutiaoScript() {
        return readScriptFromResource(R.raw.toutiao_browsing_script);
    }

    private String readScriptFromResource(int resourceId) {
        try {
            InputStream inputStream = getResources().openRawResource(resourceId);
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            StringBuilder stringBuilder = new StringBuilder();
            String line;

            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line).append("\n");
            }

            reader.close();
            inputStream.close();

            return stringBuilder.toString();
        } catch (IOException e) {
            Log.e(TAG, "Error reading script file", e);
            return "console.log('脚本文件读取失败: " + e.getMessage() + "');";
        }
    }

    @Override
    public int getIconResourceId() {
        return R.drawable.tab_icon_debug;
    }
}
